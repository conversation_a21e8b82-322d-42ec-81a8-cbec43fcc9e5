"""
Configuration settings for the desktop translation application.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional
from dotenv import load_dotenv

load_dotenv()

@dataclass
class AISettings:
    """AI engine configuration."""
    openai_api_key: str = ""
    openai_model: str = "gpt-3.5-turbo"
    claude_api_key: str = ""
    claude_model: str = "claude-3-sonnet-20240229"
    baidu_api_key: str = ""
    baidu_secret_key: str = ""
    qwen_api_key: str = ""
    qwen_model: str = "qwen-turbo"
    
    def __post_init__(self):
        # Load from environment variables
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "")
        self.claude_api_key = os.getenv("CLAUDE_API_KEY", "")
        self.baidu_api_key = os.getenv("BAIDU_API_KEY", "")
        self.baidu_secret_key = os.getenv("BAIDU_SECRET_KEY", "")
        self.qwen_api_key = os.getenv("QWEN_API_KEY", "")

@dataclass
class WindowSettings:
    """Floating window configuration."""
    width: int = 400
    height: int = 300
    max_width: int = 600
    max_height: int = 400
    margin: int = 20
    opacity: float = 0.9
    font_size: int = 14
    font_family: str = "Microsoft YaHei"
    background_color: str = "#2b2b2b"
    text_color: str = "#ffffff"
    border_color: str = "#555555"
    border_radius: int = 8

@dataclass
class BehaviorSettings:
    """Application behavior configuration."""
    auto_hide_delay: int = 3000  # milliseconds
    debounce_delay: int = 500     # milliseconds
    max_text_length: int = 5000   # characters
    enable_cache: bool = True
    cache_expiry: int = 86400     # seconds (24 hours)
    max_retries: int = 3
    timeout: int = 30            # seconds

@dataclass
class Settings:
    """Main application settings."""
    ai: AISettings
    window: WindowSettings
    behavior: BehaviorSettings
    enabled_engines: List[str] = None
    
    def __post_init__(self):
        if self.enabled_engines is None:
            self.enabled_engines = ["openai"]
    
    @classmethod
    def load(cls) -> 'Settings':
        """Load settings from configuration."""
        return cls(
            ai=AISettings(),
            window=WindowSettings(),
            behavior=BehaviorSettings()
        )
    
    def save(self) -> None:
        """Save settings to configuration."""
        # Implementation for saving settings
        pass