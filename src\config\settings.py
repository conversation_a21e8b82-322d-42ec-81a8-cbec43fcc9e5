"""
Configuration settings for the desktop translation application.
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional
from dotenv import load_dotenv

load_dotenv()

@dataclass
class AISettings:
    """AI engine configuration."""
    # 默认引擎选择
    default_engine: str = "custom"  # "openai", "claude", "baidu", "qwen", "custom"
    
    # OpenAI 原版设置
    openai_api_key: str = ""
    openai_model: str = "gpt-3.5-turbo"
    openai_base_url: str = "https://api.openai.com/v1"
    
    # Claude设置
    claude_api_key: str = ""
    claude_model: str = "claude-3-sonnet-20240229"
    
    # 百度设置
    baidu_api_key: str = ""
    baidu_secret_key: str = ""
    
    # 通义千问设置
    qwen_api_key: str = ""
    qwen_model: str = "qwen-turbo"
    
    # 第三方API设置 (Cheery Studio)
    custom_name: str = "Cheery Studio"
    custom_api_key: str = "sk-eAvXeLuCrteG9VYFFraw4KPIsuglwI1u9BIFo3vJKVe5SCdW"
    custom_base_url: str = "https://kmqepuqihpft.ap-southeast-1.clawcloudrun.com/v1"
    custom_model: str = "gemini-2.5-flash"  # 默认快速模型
    
    # 可用的第三方模型列表
    available_custom_models: List[str] = None
    
    def __post_init__(self):
        # Load from environment variables
        self.openai_api_key = os.getenv("OPENAI_API_KEY", self.openai_api_key)
        self.claude_api_key = os.getenv("CLAUDE_API_KEY", "")
        self.baidu_api_key = os.getenv("BAIDU_API_KEY", "")
        self.baidu_secret_key = os.getenv("BAIDU_SECRET_KEY", "")
        self.qwen_api_key = os.getenv("QWEN_API_KEY", "")
        
        # 初始化可用模型列表
        if self.available_custom_models is None:
            self.available_custom_models = [
                "gemini-2.5-flash",
                "gemini-2.5-flash-lite-preview-06-17",
                "GLM-4.5",
                "horizon-beta"  # GPT5测试版
            ]

@dataclass
class WindowSettings:
    """Floating window configuration."""
    width: int = 400
    height: int = 300
    max_width: int = 600
    max_height: int = 400
    margin: int = 20
    opacity: float = 0.9
    font_size: int = 14
    font_family: str = "Microsoft YaHei"
    background_color: str = "#2b2b2b"
    text_color: str = "#ffffff"
    border_color: str = "#555555"
    border_radius: int = 8

@dataclass
class BehaviorSettings:
    """Application behavior configuration."""
    auto_hide_delay: int = 0  # milliseconds - 禁用自动隐藏
    debounce_delay: int = 500     # milliseconds
    max_text_length: int = 5000   # characters
    enable_cache: bool = True
    cache_expiry: int = 86400     # seconds (24 hours)
    max_retries: int = 3
    timeout: int = 30            # seconds
    
    # 鼠标跟随设置 - 显示器同步优化
    mouse_follow_interval: int = 3   # 3ms = 300Hz 自动匹配显示器刷新率
    mouse_follow_threshold: int = 2   # 2px 超高精度
    enable_mouse_follow: bool = True  # 是否启用鼠标跟随
    smooth_follow: bool = False  # 关闭平滑以获得最快响应
    use_high_performance: bool = True  # 启用高性能模式
    display_sync: bool = True  # 启用显示器同步
    vsync_mode: bool = True  # 垂直同步模式

@dataclass
class Settings:
    """Main application settings."""
    ai: AISettings
    window: WindowSettings
    behavior: BehaviorSettings
    enabled_engines: List[str] = None
    
    def __post_init__(self):
        if self.enabled_engines is None:
            self.enabled_engines = ["openai"]
    
    @classmethod
    def load(cls) -> 'Settings':
        """Load settings from configuration."""
        return cls(
            ai=AISettings(),
            window=WindowSettings(),
            behavior=BehaviorSettings()
        )
    
    def save(self) -> None:
        """Save settings to configuration."""
        # Implementation for saving settings
        pass