#!/usr/bin/env python3
"""
启动超高帧率翻译程序 - 与显示器Hz完美同步
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt6.QtWidgets import QApplication
from src.config.settings import Settings
from src.gui.floating_window import FloatingWindow

def main():
    print("🚀 启动超高帧率翻译程序...")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    # 显示器信息
    screen = app.primaryScreen()
    hz = screen.refreshRate()
    
    print(f"🖥️ 您的显示器: {hz} Hz")
    print(f"⚡ 程序将以 {hz} FPS 运行")
    print(f"🎯 同步间隔: {int(1000/hz)}ms")
    print()
    
    # 加载优化配置 - 自动同步显示器Hz
    settings = Settings.load()
    ideal_interval = int(1000 / hz)
    settings.behavior.mouse_follow_interval = ideal_interval  # 自动匹配显示器Hz
    settings.behavior.mouse_follow_threshold = 2  # 2px 超精细跟随
    settings.behavior.auto_hide_delay = 0  # 禁用自动隐藏
    settings.behavior.display_sync = True
    settings.behavior.vsync_mode = True
    
    print(f"⚙️ 自动同步配置完成:")
    print(f"   同步间隔: {ideal_interval}ms")
    print(f"   完美匹配: {hz}Hz 显示器")
    
    # 创建窗口
    window = FloatingWindow(settings)
    
    print("✅ 超高帧率翻译程序已启动")
    print("📋 复制英文文本即可看到翻译跟随效果")
    print("🖱️ 移动鼠标感受丝滑流畅的跟随")
    print()
    print(f"🎉 享受与{hz}Hz显示器完美同步的翻译体验！")
    print("💫 窗口跟随现在与显示器刷新率完全匹配")
    print("⏹️ 按 Ctrl+C 退出程序")
    
    try:
        app.exec()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")

if __name__ == "__main__":
    main()