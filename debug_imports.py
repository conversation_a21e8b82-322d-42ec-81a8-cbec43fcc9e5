#!/usr/bin/env python3
"""
调试导入问题的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_step_by_step():
    """逐步测试导入"""
    try:
        print("🔍 步骤1: 测试PyQt6基础导入...")
        from PyQt6.QtWidgets import QApplication, QWidget
        from PyQt6.QtCore import Qt
        print("✅ PyQt6基础导入成功")
        
        print("🔍 步骤2: 测试配置模块...")
        from src.config.settings import Settings
        print("✅ 设置模块导入成功")
        
        print("🔍 步骤3: 测试常量模块...")
        from src.config.constants import AIEngine
        print("✅ 常量模块导入成功")
        
        print("🔍 步骤4: 测试核心模块...")
        from src.core.text_processor import TextProcessor
        print("✅ 文本处理器导入成功")
        
        print("🔍 步骤5: 测试GUI模块...")
        from src.gui.floating_window import FloatingWindow
        print("✅ 浮动窗口导入成功")
        
        from src.gui.settings_dialog import SettingsDialog
        print("✅ 设置对话框导入成功")
        
        print("🔍 步骤6: 测试工具模块...")
        from src.utils.logger import get_logger
        print("✅ 日志工具导入成功")
        
        print("\n🎉 所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_step_by_step()
    if success:
        print("\n🚀 尝试启动应用程序...")
        try:
            from src.main import main
            main()
        except Exception as e:
            print(f"❌ 应用启动失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\n❌ 导入测试失败，无法启动应用程序")
