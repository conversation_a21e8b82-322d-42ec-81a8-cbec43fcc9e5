README.md
pyproject.toml
desktop_translator.egg-info/PKG-INFO
desktop_translator.egg-info/SOURCES.txt
desktop_translator.egg-info/dependency_links.txt
desktop_translator.egg-info/entry_points.txt
desktop_translator.egg-info/requires.txt
desktop_translator.egg-info/top_level.txt
src/__init__.py
src/main.py
src/ai_engines/__init__.py
src/ai_engines/baidu_engine.py
src/ai_engines/base_engine.py
src/ai_engines/claude_engine.py
src/ai_engines/openai_engine.py
src/ai_engines/qwen_engine.py
src/config/__init__.py
src/config/constants.py
src/config/settings.py
src/core/__init__.py
src/core/clipboard_monitor.py
src/core/text_processor.py
src/core/translator.py
src/gui/__init__.py
src/gui/floating_window.py
src/gui/settings_dialog.py
src/utils/__init__.py
src/utils/cache_manager.py
src/utils/logger.py
src/utils/mouse_tracker.py
tests/test_cache_manager.py
tests/test_clipboard_monitor.py
tests/test_text_processor.py
tests/test_translator.py