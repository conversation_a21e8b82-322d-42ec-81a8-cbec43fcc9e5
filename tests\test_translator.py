"""
Test suite for the translator module.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from src.core.translator import Translator
from src.config import Settings, AIEngine
from src.ai_engines.base_engine import TranslationResult

class TestTranslator:
    """Test cases for Translator."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.settings = Settings.load()
        self.translator = Translator(self.settings)
    
    def teardown_method(self):
        """Cleanup test fixtures."""
        # Clean up engines
        for engine in self.translator.engines.values():
            import asyncio
            asyncio.create_task(engine.close())
    
    def test_initialization(self):
        """Test translator initialization."""
        assert isinstance(self.translator.engines, dict)
        assert isinstance(self.translator.cache_manager, object)
        assert len(self.translator.engines) >= 0
    
    def test_select_engine_preferred(self):
        """Test engine selection with preferred engine."""
        # Mock engine
        mock_engine = Mock()
        mock_engine.get_engine_name.return_value = "openai"
        self.translator.engines["openai"] = mock_engine
        
        result = self.translator._select_engine("openai")
        assert result == mock_engine
    
    def test_select_engine_enabled(self):
        """Test engine selection from enabled engines."""
        # Mock engines
        mock_engine1 = Mock()
        mock_engine1.get_engine_name.return_value = "openai"
        mock_engine2 = Mock()
        mock_engine2.get_engine_name.return_value = "claude"
        
        self.translator.engines["openai"] = mock_engine1
        self.translator.engines["claude"] = mock_engine2
        self.settings.enabled_engines = ["claude"]
        
        result = self.translator._select_engine()
        assert result == mock_engine2
    
    def test_select_engine_any_available(self):
        """Test engine selection when no preferred engine specified."""
        # Mock engine
        mock_engine = Mock()
        mock_engine.get_engine_name.return_value = "openai"
        self.translator.engines["openai"] = mock_engine
        
        result = self.translator._select_engine()
        assert result == mock_engine
    
    def test_select_engine_none_available(self):
        """Test engine selection when no engines available."""
        result = self.translator._select_engine()
        assert result is None
    
    def test_get_available_engines(self):
        """Test getting available engines."""
        # Mock engines
        self.translator.engines = {
            "openai": Mock(),
            "claude": Mock()
        }
        
        result = self.translator.get_available_engines()
        assert set(result) == {"openai", "claude"}
    
    def test_is_engine_configured_true(self):
        """Test engine configuration check for configured engine."""
        # Mock engine
        mock_engine = Mock()
        mock_engine.is_configured.return_value = True
        self.translator.engines["openai"] = mock_engine
        
        result = self.translator.is_engine_configured("openai")
        assert result is True
    
    def test_is_engine_configured_false(self):
        """Test engine configuration check for unconfigured engine."""
        # Mock engine
        mock_engine = Mock()
        mock_engine.is_configured.return_value = False
        self.translator.engines["openai"] = mock_engine
        
        result = self.translator.is_engine_configured("openai")
        assert result is False
    
    def test_is_engine_configured_not_exists(self):
        """Test engine configuration check for non-existent engine."""
        result = self.translator.is_engine_configured("nonexistent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_translate_empty_text(self):
        """Test translation with empty text."""
        result = await self.translator.translate("")
        
        assert result.original_text == ""
        assert result.translated_text == ""
        assert result.engine == "none"
    
    @pytest.mark.asyncio
    async def test_translate_whitespace_only(self):
        """Test translation with whitespace only."""
        result = await self.translator.translate("   ")
        
        assert result.original_text == "   "
        assert result.translated_text == ""
        assert result.engine == "none"
    
    @pytest.mark.asyncio
    async def test_translate_no_engine_available(self):
        """Test translation when no engine is available."""
        # Clear engines
        self.translator.engines.clear()
        
        result = await self.translator.translate("Hello")
        
        assert result.original_text == "Hello"
        assert result.translated_text == "No AI engine available"
        assert result.engine == "none"
    
    @pytest.mark.asyncio
    async def test_translate_cache_hit(self):
        """Test translation with cache hit."""
        # Mock cache
        cached_result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        
        self.translator.cache_manager.get = Mock(return_value=cached_result)
        
        result = await self.translator.translate("Hello", "es")
        
        assert result == cached_result
        self.translator.cache_manager.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_translate_cache_miss(self):
        """Test translation with cache miss."""
        # Mock cache and engine
        self.translator.cache_manager.get = Mock(return_value=None)
        
        mock_engine = Mock()
        mock_engine.translate = AsyncMock(return_value=TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        ))
        mock_engine.__aenter__ = AsyncMock(return_value=mock_engine)
        mock_engine.__aexit__ = AsyncMock(return_value=None)
        
        self.translator.engines["openai"] = mock_engine
        
        result = await self.translator.translate("Hello", "es")
        
        assert result.translated_text == "Hola"
        mock_engine.translate.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_translate_engine_error(self):
        """Test translation with engine error."""
        # Mock cache and engine
        self.translator.cache_manager.get = Mock(return_value=None)
        
        mock_engine = Mock()
        mock_engine.translate = AsyncMock(side_effect=Exception("Engine error"))
        mock_engine.__aenter__ = AsyncMock(return_value=mock_engine)
        mock_engine.__aexit__ = AsyncMock(return_value=None)
        mock_engine.get_engine_name.return_value = "openai"
        
        self.translator.engines["openai"] = mock_engine
        
        result = await self.translator.translate("Hello")
        
        assert "Translation error" in result.translated_text
        assert result.confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_detect_language(self):
        """Test language detection."""
        # Mock engine
        mock_engine = Mock()
        mock_engine.detect_language = AsyncMock(return_value="en")
        mock_engine.__aenter__ = AsyncMock(return_value=mock_engine)
        mock_engine.__aexit__ = AsyncMock(return_value=None)
        
        self.translator.engines["openai"] = mock_engine
        
        result = await self.translator.detect_language("Hello")
        
        assert result == "en"
        mock_engine.detect_language.assert_called_once_with("Hello")
    
    @pytest.mark.asyncio
    async def test_detect_language_no_engine(self):
        """Test language detection when no engine available."""
        self.translator.engines.clear()
        
        result = await self.translator.detect_language("Hello")
        
        assert result == "auto"
    
    def test_get_cache_stats(self):
        """Test getting cache statistics."""
        # Mock cache manager
        mock_stats = {
            "memory_entries": 10,
            "file_entries": 5,
            "total_entries": 15
        }
        self.translator.cache_manager.get_stats = Mock(return_value=mock_stats)
        
        result = self.translator.get_cache_stats()
        
        assert result == mock_stats
        self.translator.cache_manager.get_stats.assert_called_once()
    
    def test_clear_cache(self):
        """Test clearing cache."""
        self.translator.cache_manager.clear = Mock()
        
        self.translator.clear_cache()
        
        self.translator.cache_manager.clear.assert_called_once()
    
    def test_update_settings(self):
        """Test updating settings."""
        new_settings = Settings.load()
        new_settings.enabled_engines = ["claude"]
        
        # Mock engines closing
        mock_engine = Mock()
        self.translator.engines["openai"] = mock_engine
        
        self.translator.update_settings(new_settings)
        
        assert self.translator.settings == new_settings
        # Note: In real implementation, this would also test engine reinitialization