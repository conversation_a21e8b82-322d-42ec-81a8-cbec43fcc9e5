Metadata-Version: 2.4
Name: desktop-translator
Version: 1.0.0
Summary: AI-powered desktop translation tool
Author-email: Translation App Team <<EMAIL>>
License: MIT
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: End Users/Desktop
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Office/Business
Classifier: Topic :: Text Processing :: Linguistic
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: PyQt6>=6.4.0
Requires-Dist: pyperclip>=1.8.2
Requires-Dist: keyboard>=0.13.5
Requires-Dist: aiohttp>=3.8.5
Requires-Dist: openai>=1.3.0
Requires-Dist: anthropic>=0.3.0
Requires-Dist: requests>=2.31.0
Requires-Dist: numpy>=1.24.3
Requires-Dist: pillow>=10.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: loguru>=0.7.0
Requires-Dist: pydantic>=2.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Provides-Extra: build
Requires-Dist: pyinstaller>=5.0.0; extra == "build"
Requires-Dist: nsis>=0.1.0; extra == "build"

# 桌面窗口跟随翻译软件

## 项目概述
基于AI技术的智能桌面翻译工具，支持多种AI翻译引擎，实现实时剪贴板监控和智能翻译显示。

## 技术栈
- **编程语言**: Python
- **GUI框架**: PyQt6
- **AI集成**: OpenAI, Claude, 文心一言, 通义千问
- **系统监控**: pyperclip, keyboard
- **异步处理**: asyncio, aiohttp

## 核心功能模块
1. **系统监控层** - 剪贴板变化监听
2. **文本处理层** - 文本清理和语言检测
3. **AI翻译服务层** - 多AI引擎翻译
4. **定位跟踪层** - 鼠标位置跟踪
5. **界面展示层** - 悬浮窗显示

## 项目结构
```
桌面窗口翻译程序/
├── src/
│   ├── __init__.py
│   ├── main.py                 # 主程序入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # 配置管理
│   │   └── constants.py        # 常量定义
│   ├── core/
│   │   ├── __init__.py
│   │   ├── clipboard_monitor.py # 剪贴板监控
│   │   ├── text_processor.py    # 文本处理
│   │   └── translator.py        # AI翻译服务
│   ├── gui/
│   │   ├── __init__.py
│   │   ├── floating_window.py   # 悬浮窗
│   │   └── settings_dialog.py   # 设置对话框
│   ├── ai_engines/
│   │   ├── __init__.py
│   │   ├── base_engine.py      # AI引擎基类
│   │   ├── openai_engine.py    # ChatGPT
│   │   ├── claude_engine.py    # Claude
│   │   ├── baidu_engine.py     # 文心一言
│   │   └── qwen_engine.py      # 通义千问
│   └── utils/
│       ├── __init__.py
│       ├── mouse_tracker.py    # 鼠标跟踪
│       ├── cache_manager.py    # 缓存管理
│       └── logger.py           # 日志管理
├── resources/
│   ├── icons/
│   └── styles/
├── tests/
├── requirements.txt
├── README.md
└── .gitignore
```

## 开发命令
```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python src/main.py

# 运行测试
python -m pytest tests/

# 代码检查
flake8 src/
black src/
```

## 代码风格
- 遵循PEP 8规范
- 使用类型注解
- 异步编程优先
- 错误处理完善
- 模块化设计
