#!/usr/bin/env python3
"""
剪贴板监控测试脚本
用于诊断剪贴板监控功能的问题
"""

import sys
import os
import asyncio
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_pyperclip():
    """测试pyperclip基础功能"""
    print("🔍 测试pyperclip基础功能...")
    try:
        import pyperclip
        
        # 测试读取剪贴板
        current_content = pyperclip.paste()
        print(f"✅ 当前剪贴板内容: '{current_content}'")
        
        # 测试写入剪贴板
        test_text = f"测试文本 {time.time()}"
        pyperclip.copy(test_text)
        print(f"✅ 已写入测试文本: '{test_text}'")
        
        # 验证写入
        new_content = pyperclip.paste()
        if new_content == test_text:
            print("✅ pyperclip读写功能正常")
            return True
        else:
            print(f"❌ pyperclip写入失败: 期望'{test_text}', 实际'{new_content}'")
            return False
            
    except Exception as e:
        print(f"❌ pyperclip测试失败: {e}")
        return False

def test_clipboard_monitor():
    """测试剪贴板监控器"""
    print("\n🔍 测试剪贴板监控器...")
    try:
        from src.core.clipboard_monitor import ClipboardMonitor
        
        monitor = ClipboardMonitor()
        
        # 设置回调函数
        detected_texts = []
        
        def on_clipboard_change(text):
            print(f"📋 检测到剪贴板变化: '{text}'")
            detected_texts.append(text)
        
        monitor.add_callback(on_clipboard_change)
        
        # 连接信号
        def on_signal(text):
            print(f"📡 收到信号: '{text}'")
        
        monitor.clipboard_changed.connect(on_signal)
        
        print("✅ 剪贴板监控器创建成功")
        return monitor, detected_texts
        
    except Exception as e:
        print(f"❌ 剪贴板监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

async def test_monitor_loop():
    """测试监控循环"""
    print("\n🔍 测试监控循环...")
    
    monitor, detected_texts = test_clipboard_monitor()
    if not monitor:
        return False
    
    try:
        # 启动监控
        await monitor.start()
        print("✅ 监控已启动")
        
        # 等待一段时间让用户测试
        print("\n📋 请在接下来的10秒内复制一些文本来测试...")
        print("   (复制不同的文本，观察是否被检测到)")
        
        for i in range(10):
            print(f"⏰ 倒计时: {10-i} 秒", end="\r")
            await asyncio.sleep(1)
        
        print("\n")
        
        # 停止监控
        await monitor.stop()
        print("✅ 监控已停止")
        
        # 显示结果
        print(f"\n📊 检测结果:")
        print(f"   检测到的文本数量: {len(detected_texts)}")
        for i, text in enumerate(detected_texts):
            print(f"   {i+1}. '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        return len(detected_texts) > 0
        
    except Exception as e:
        print(f"❌ 监控循环测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qt_integration():
    """测试Qt集成"""
    print("\n🔍 测试Qt集成...")
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        
        app = QApplication(sys.argv)
        
        from src.core.clipboard_monitor import ClipboardMonitor
        monitor = ClipboardMonitor()
        
        signal_received = []
        
        def on_signal(text):
            print(f"📡 Qt信号收到: '{text}'")
            signal_received.append(text)
            app.quit()  # 收到信号后退出
        
        monitor.clipboard_changed.connect(on_signal)
        
        # 启动监控
        async def start_monitor():
            await monitor.start()
        
        # 在Qt事件循环中运行
        import threading
        def run_async():
            asyncio.run(start_monitor())
        
        async_thread = threading.Thread(target=run_async, daemon=True)
        async_thread.start()
        
        # 设置超时
        QTimer.singleShot(5000, app.quit)  # 5秒后自动退出
        
        print("✅ Qt应用启动，请复制文本测试...")
        app.exec()
        
        return len(signal_received) > 0
        
    except Exception as e:
        print(f"❌ Qt集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 剪贴板监控诊断工具")
    print("=" * 50)
    
    # 测试1: pyperclip基础功能
    if not test_pyperclip():
        print("\n❌ pyperclip基础功能测试失败，无法继续")
        return
    
    # 测试2: 剪贴板监控器
    monitor, _ = test_clipboard_monitor()
    if not monitor:
        print("\n❌ 剪贴板监控器创建失败，无法继续")
        return
    
    # 测试3: 异步监控循环
    print("\n🚀 开始异步监控测试...")
    try:
        result = asyncio.run(test_monitor_loop())
        if result:
            print("✅ 异步监控测试成功")
        else:
            print("⚠️  异步监控测试未检测到剪贴板变化")
    except Exception as e:
        print(f"❌ 异步监控测试失败: {e}")
    
    # 测试4: Qt集成（可选）
    print("\n🎯 是否测试Qt集成? (y/n): ", end="")
    try:
        choice = input().lower()
        if choice == 'y':
            if test_qt_integration():
                print("✅ Qt集成测试成功")
            else:
                print("⚠️  Qt集成测试未检测到剪贴板变化")
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    
    print("\n🏁 诊断完成!")

if __name__ == "__main__":
    main()
