"""
Settings dialog for configuring the translation application.
"""

import os
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QSpinBox, QCheckBox, QTabWidget,
    QGroupBox, QFormLayout, QMessageBox, QDialogButtonBox,
    QTextEdit, QSlider, QWidget
)
from PyQt6.QtGui import QFont, QIntValidator
from PyQt6.QtCore import Qt, pyqtSignal

from ..config import Settings, AIEngine
from ..utils.logger import get_logger

logger = get_logger(__name__)

class SettingsDialog(QDialog):
    """Settings dialog for the application."""
    
    settings_changed = pyqtSignal(Settings)
    
    def __init__(self, settings: Settings, parent=None):
        super().__init__(parent)
        self.settings = settings
        self.original_settings = settings
        self._setup_ui()
        self._load_settings()
        
        logger.info("Settings dialog initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        self.setWindowTitle("🚀 桌面翻译程序 - 设置中心")
        self.setModal(True)
        self.setMinimumSize(700, 600)
        self.resize(700, 600)
        
        # 设置现代化样式
        self.setStyleSheet("""
            QDialog {
                background: #f8f9fa;
                color: #212529;
            }
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                background-color: white;
                border-radius: 8px;
                margin-top: 10px;
            }
            QTabBar::tab {
                background: #e9ecef;
                color: #495057;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 6px 6px 0 0;
                font-weight: bold;
                font-size: 13px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background: #4CAF50;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background: #d4edda;
                color: #155724;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #495057;
                font-size: 14px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Create tabs
        tabs = QTabWidget()
        
        # AI Settings tab
        ai_tab = self._create_ai_tab()
        tabs.addTab(ai_tab, "🤖 AI引擎")
        
        # Window Settings tab
        window_tab = self._create_window_tab()
        tabs.addTab(window_tab, "🖼️ 窗口设置")
        
        # Behavior Settings tab
        behavior_tab = self._create_behavior_tab()
        tabs.addTab(behavior_tab, "⚙️ 行为设置")
        
        layout.addWidget(tabs)
        
        # Buttons with modern styling
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel | 
            QDialogButtonBox.StandardButton.Apply
        )
        
        # 设置按钮文本
        button_box.button(QDialogButtonBox.StandardButton.Ok).setText("✅ 确定")
        button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("❌ 取消")
        button_box.button(QDialogButtonBox.StandardButton.Apply).setText("🔄 应用")
        
        # 按钮样式
        button_box.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                min-width: 80px;
            }
            QPushButton[text*="确定"] {
                background-color: #28a745;
                color: white;
            }
            QPushButton[text*="确定"]:hover {
                background-color: #218838;
            }
            QPushButton[text*="取消"] {
                background-color: #dc3545;
                color: white;
            }
            QPushButton[text*="取消"]:hover {
                background-color: #c82333;
            }
            QPushButton[text*="应用"] {
                background-color: #17a2b8;
                color: white;
            }
            QPushButton[text*="应用"]:hover {
                background-color: #138496;
            }
        """)
        
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        
        layout.addWidget(button_box)
    
    def _create_ai_tab(self) -> QWidget:
        """Create AI engine settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 默认引擎选择
        engine_group = QGroupBox("🚀 默认AI引擎")
        engine_layout = QFormLayout(engine_group)
        
        self.engine_combo = QComboBox()
        self.engine_combo.addItems([
            "🤖 第三方API (Cheery Studio)",
            "🚀 OpenAI GPT", 
            "🧠 Claude (Anthropic)",
            "🐼 百度文心一言",
            "🔮 通义千问"
        ])
        self.engine_combo.currentTextChanged.connect(self._on_engine_changed)
        
        engine_layout.addRow("🎯 选择引擎:", self.engine_combo)
        layout.addWidget(engine_group)
        
        # 第三方API设置 (Cheery Studio)
        self.custom_group = QGroupBox("🤖 第三方API设置 (当前: Cheery Studio)")
        custom_layout = QFormLayout(self.custom_group)
        
        self.custom_name_edit = QLineEdit()
        self.custom_name_edit.setPlaceholderText("API服务名称")
        custom_layout.addRow("🏷️ 服务名称:", self.custom_name_edit)
        
        self.custom_url_edit = QLineEdit()
        self.custom_url_edit.setPlaceholderText("https://your-api-url.com/v1")
        custom_layout.addRow("🌐 API地址:", self.custom_url_edit)
        
        self.custom_key_edit = QLineEdit()
        self.custom_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.custom_key_edit.setPlaceholderText("输入API密钥")
        custom_layout.addRow("🔑 API密钥:", self.custom_key_edit)
        
        self.custom_model_combo = QComboBox()
        self.custom_model_combo.setEditable(True)
        self.custom_model_combo.addItems([
            "🚀 gemini-2.5-flash (推荐)",
            "⚡ gemini-2.5-flash-lite-preview-06-17",
            "🧠 GLM-4.5", 
            "🔮 horizon-beta (GPT5测试版)"
        ])
        custom_layout.addRow("🚀 模型:", self.custom_model_combo)
        
        # 测试按钮
        test_btn = QPushButton("🧪 测试连接")
        test_btn.clicked.connect(self._test_custom_api)
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        custom_layout.addRow("", test_btn)
        
        layout.addWidget(self.custom_group)
        
        # OpenAI Settings
        self.openai_group = QGroupBox("🚀 OpenAI 官方设置")
        openai_layout = QFormLayout(self.openai_group)
        
        self.openai_key_edit = QLineEdit()
        self.openai_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.openai_key_edit.setPlaceholderText("输入OpenAI API密钥")
        
        self.openai_model_edit = QLineEdit()
        self.openai_model_edit.setPlaceholderText("gpt-3.5-turbo")
        
        openai_layout.addRow("🔑 API密钥:", self.openai_key_edit)
        openai_layout.addRow("🤖 模型:", self.openai_model_edit)
        
        layout.addWidget(self.openai_group)
        
        # Claude Settings
        self.claude_group = QGroupBox("🧠 Claude (Anthropic) 设置")
        claude_layout = QFormLayout(self.claude_group)
        self.claude_key_edit = QLineEdit()
        self.claude_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.claude_key_edit.setPlaceholderText("输入Claude API密钥")
        self.claude_model_edit = QLineEdit()
        self.claude_model_edit.setPlaceholderText("claude-3-sonnet-20240229")
        claude_layout.addRow("🔑 API密钥:", self.claude_key_edit)
        claude_layout.addRow("🤖 模型:", self.claude_model_edit)
        layout.addWidget(self.claude_group)

        # Baidu Settings
        self.baidu_group = QGroupBox("🐼 百度文心一言设置")
        baidu_layout = QFormLayout(self.baidu_group)
        self.baidu_key_edit = QLineEdit()
        self.baidu_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.baidu_key_edit.setPlaceholderText("输入百度API密钥")
        self.baidu_secret_key_edit = QLineEdit()
        self.baidu_secret_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.baidu_secret_key_edit.setPlaceholderText("输入百度Secret Key")
        baidu_layout.addRow("🔑 API密钥:", self.baidu_key_edit)
        baidu_layout.addRow("🔐 Secret Key:", self.baidu_secret_key_edit)
        layout.addWidget(self.baidu_group)

        # Qwen Settings
        self.qwen_group = QGroupBox("🔮 通义千问设置")
        qwen_layout = QFormLayout(self.qwen_group)
        self.qwen_key_edit = QLineEdit()
        self.qwen_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.qwen_key_edit.setPlaceholderText("输入通义千问API密钥")
        self.qwen_model_edit = QLineEdit()
        self.qwen_model_edit.setPlaceholderText("qwen-turbo")
        qwen_layout.addRow("🔑 API密钥:", self.qwen_key_edit)
        qwen_layout.addRow("🤖 模型:", self.qwen_model_edit)
        layout.addWidget(self.qwen_group)
        
        layout.addStretch()
        
        # Map engine names to their group boxes
        self.engine_groups = {
            "🤖 第三方API (Cheery Studio)": self.custom_group,
            "🚀 OpenAI GPT": self.openai_group,
            "🧠 Claude (Anthropic)": self.claude_group,
            "🐼 百度文心一言": self.baidu_group,
            "🔮 通义千问": self.qwen_group,
        }
        
        # Initial display update
        self._on_engine_changed(self.engine_combo.currentText())
        
        return widget
        
    def _create_window_tab(self) -> QWidget:
        """Create window settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Appearance Group
        appearance_group = QGroupBox("🎨 外观设置")
        appearance_layout = QFormLayout(appearance_group)
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(30, 100)
        self.opacity_label = QLabel("90%")
        
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        
        appearance_layout.addRow("透明度:", opacity_layout)
        
        layout.addWidget(appearance_group)
        layout.addStretch()
        
        return widget
    
    def _create_behavior_tab(self) -> QWidget:
        """Create behavior settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Timing Group
        timing_group = QGroupBox("⏰ 时间设置")
        timing_layout = QFormLayout(timing_group)
        
        self.auto_hide_spin = QSpinBox()
        self.auto_hide_spin.setRange(0, 10000)
        self.auto_hide_spin.setSuffix(" ms")
        self.auto_hide_spin.setValue(0)  # 默认不自动隐藏
        
        self.debounce_spin = QSpinBox()
        self.debounce_spin.setRange(100, 2000)
        self.debounce_spin.setSuffix(" ms")
        
        timing_layout.addRow("自动隐藏延迟:", self.auto_hide_spin)
        timing_layout.addRow("防抖延迟:", self.debounce_spin)
        
        # Clipboard Filtering Group
        clipboard_filter_group = QGroupBox("剪贴板过滤")
        clipboard_filter_layout = QFormLayout(clipboard_filter_group)

        self.min_text_length_spin = QSpinBox()
        self.min_text_length_spin.setRange(1, 50) # Assuming a reasonable range for min length
        self.min_text_length_spin.setSuffix(" 字符")
        clipboard_filter_layout.addRow("最小翻译文本长度:", self.min_text_length_spin)

        self.enable_sensitive_filter_checkbox = QCheckBox("启用敏感文本过滤")
        clipboard_filter_layout.addRow("", self.enable_sensitive_filter_checkbox)

        # Hotkey setting
        hotkey_group = QGroupBox("快捷键设置")
        hotkey_layout = QFormLayout(hotkey_group)
        self.hotkey_edit = QLineEdit()
        hotkey_layout.addRow("显示/隐藏窗口快捷键:", self.hotkey_edit)
        
        layout.addWidget(timing_group)
        layout.addWidget(clipboard_filter_group)
        layout.addWidget(hotkey_group) # Add the new group
        layout.addStretch()
        
        return widget
        
    def _load_settings(self) -> None:
        """Load current settings into the dialog."""
        ai_settings = self.settings.ai
        window_settings = self.settings.window
        behavior_settings = self.settings.behavior
        
        # AI settings
        if hasattr(ai_settings, 'default_engine'):
            engine_map = {
                "custom": "🤖 第三方API (Cheery Studio)",
                "openai": "🚀 OpenAI GPT",
                "claude": "🧠 Claude (Anthropic)", 
                "wenxin": "🐼 百度文心一言",
                "qianwen": "🔮 通义千问"
            }
            engine_text = engine_map.get(ai_settings.default_engine, "🤖 第三方API (Cheery Studio)")
            self.engine_combo.setCurrentText(engine_text)
            
        # Custom API settings
        if hasattr(ai_settings, 'custom_name'):
            self.custom_name_edit.setText(ai_settings.custom_name)
        if hasattr(ai_settings, 'custom_base_url'):
            self.custom_url_edit.setText(ai_settings.custom_base_url)
        if hasattr(ai_settings, 'custom_api_key'):
            self.custom_key_edit.setText(ai_settings.custom_api_key)
        if hasattr(ai_settings, 'custom_model'):
            self.custom_model_combo.setCurrentText(ai_settings.custom_model)
            
        # OpenAI settings
        self.openai_key_edit.setText(ai_settings.openai_api_key)
        self.openai_model_edit.setText(ai_settings.openai_model)

        # Claude settings
        if hasattr(ai_settings, 'claude_api_key'):
            self.claude_key_edit.setText(ai_settings.claude_api_key)
        if hasattr(ai_settings, 'claude_model'):
            self.claude_model_edit.setText(ai_settings.claude_model)

        # Baidu settings
        if hasattr(ai_settings, 'baidu_api_key'):
            self.baidu_key_edit.setText(ai_settings.baidu_api_key)
        if hasattr(ai_settings, 'baidu_secret_key'):
            self.baidu_secret_key_edit.setText(ai_settings.baidu_secret_key)

        # Qwen settings
        if hasattr(ai_settings, 'qwen_api_key'):
            self.qwen_key_edit.setText(ai_settings.qwen_api_key)
        if hasattr(ai_settings, 'qwen_model'):
            self.qwen_model_edit.setText(ai_settings.qwen_model)
        
        # Window settings
        self.opacity_slider.setValue(int(window_settings.opacity * 100))
        
        # Behavior settings
        self.auto_hide_spin.setValue(behavior_settings.auto_hide_delay)
        self.debounce_spin.setValue(behavior_settings.debounce_delay)
        self.min_text_length_spin.setValue(behavior_settings.min_text_length)
        self.enable_sensitive_filter_checkbox.setChecked(behavior_settings.enable_sensitive_filter)
        self.hotkey_edit.setText(behavior_settings.hotkey_toggle_window)
        
    def apply_settings(self) -> None:
        """Apply current settings."""
        try:
            # Create new settings from form data
            new_settings = self._collect_settings()
            
            # Emit signal to update main application
            self.settings_changed.emit(new_settings)
            
            # Update internal settings
            self.settings = new_settings
            
            QMessageBox.information(self, "设置", "设置已应用")
            logger.info("Settings applied successfully")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用设置失败: {str(e)}")
            logger.error(f"Failed to apply settings: {e}")
            
    def _collect_settings(self) -> Settings:
        """Collect settings from the form."""
        # 简化的设置收集，只更新必要的字段
        settings = Settings.load()
        
        # AI settings
        # Map display text to internal engine name
        engine_display_to_internal_map = {
            "🤖 第三方API (Cheery Studio)": "custom",
            "🚀 OpenAI GPT": "openai",
            "🧠 Claude (Anthropic)": "claude",
            "🐼 百度文心一言": "baidu",
            "🔮 通义千问": "qwen",
        }
        settings.ai.default_engine = engine_display_to_internal_map.get(
            self.engine_combo.currentText(), 
            "custom" # Default fallback
        )

        settings.ai.custom_name = self.custom_name_edit.text()
        settings.ai.custom_base_url = self.custom_url_edit.text()
        settings.ai.custom_api_key = self.custom_key_edit.text()
        settings.ai.custom_model = self.custom_model_combo.currentText()
        settings.ai.openai_api_key = self.openai_key_edit.text()
        settings.ai.openai_model = self.openai_model_edit.text()

        # Claude settings
        settings.ai.claude_api_key = self.claude_key_edit.text()
        settings.ai.claude_model = self.claude_model_edit.text()

        # Baidu settings
        settings.ai.baidu_api_key = self.baidu_key_edit.text()
        settings.ai.baidu_secret_key = self.baidu_secret_key_edit.text()

        # Qwen settings
        settings.ai.qwen_api_key = self.qwen_key_edit.text()
        settings.ai.qwen_model = self.qwen_model_edit.text()
        
        # Window settings
        settings.window.opacity = self.opacity_slider.value() / 100.0
        
        # Behavior settings
        settings.behavior.auto_hide_delay = self.auto_hide_spin.value()
        settings.behavior.debounce_delay = self.debounce_spin.value()
        settings.behavior.min_text_length = self.min_text_length_spin.value()
        settings.behavior.enable_sensitive_filter = self.enable_sensitive_filter_checkbox.isChecked()
        settings.behavior.hotkey_toggle_window = self.hotkey_edit.text()
        
        return settings
        
    def _on_engine_changed(self, text: str):
        """Handle engine selection change."""
        selected_engine = self.engine_combo.currentText()
        for engine_name, group_box in self.engine_groups.items():
            if engine_name == selected_engine:
                group_box.show()
            else:
                group_box.hide()
        
    def _test_custom_api(self):
        """Test custom API connection."""
        QMessageBox.information(self, "测试", "API连接测试功能开发中...")