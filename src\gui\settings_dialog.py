"""
Settings dialog for configuring the translation application.
"""

import os
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QSpinBox, QCheckBox, QTabWidget,
    QGroupBox, QFormLayout, QMessageBox, QDialogButtonBox,
    QTextEdit, QSlider, QWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from ..config import Settings, AIEngine
from ..utils.logger import get_logger

logger = get_logger(__name__)

class SettingsDialog(QDialog):
    """Settings dialog for configuring the application."""
    
    settings_changed = pyqtSignal(Settings)  # Signal emitted when settings change
    
    def __init__(self, settings: Settings, parent=None):
        super().__init__(parent)
        self.settings = settings
        self.original_settings = settings
        self._setup_ui()
        self._load_settings()
        
        logger.info("Settings dialog initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        self.setWindowTitle("翻译设置")
        self.setModal(True)
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Create tabs
        tabs = QTabWidget()
        
        # AI Settings tab
        ai_tab = self._create_ai_tab()
        tabs.addTab(ai_tab, "AI引擎")
        
        # Window Settings tab
        window_tab = self._create_window_tab()
        tabs.addTab(window_tab, "窗口设置")
        
        # Behavior Settings tab
        behavior_tab = self._create_behavior_tab()
        tabs.addTab(behavior_tab, "行为设置")
        
        layout.addWidget(tabs)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel | 
            QDialogButtonBox.StandardButton.Apply
        )
        
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        
        layout.addWidget(button_box)
    
    def _create_ai_tab(self) -> QWidget:
        """Create AI engine settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # OpenAI Settings
        openai_group = QGroupBox("OpenAI 设置")
        openai_layout = QFormLayout(openai_group)
        
        self.openai_api_key = QLineEdit()
        self.openai_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.openai_model = QComboBox()
        self.openai_model.addItems(["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"])
        
        openai_layout.addRow("API Key:", self.openai_api_key)
        openai_layout.addRow("模型:", self.openai_model)
        
        # Claude Settings
        claude_group = QGroupBox("Claude 设置")
        claude_layout = QFormLayout(claude_group)
        
        self.claude_api_key = QLineEdit()
        self.claude_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.claude_model = QComboBox()
        self.claude_model.addItems(["claude-3-sonnet-20240229", "claude-3-opus-20240229"])
        
        claude_layout.addRow("API Key:", self.claude_api_key)
        claude_layout.addRow("模型:", self.claude_model)
        
        # Baidu Settings
        baidu_group = QGroupBox("百度文心一言设置")
        baidu_layout = QFormLayout(baidu_group)
        
        self.baidu_api_key = QLineEdit()
        self.baidu_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.baidu_secret_key = QLineEdit()
        self.baidu_secret_key.setEchoMode(QLineEdit.EchoMode.Password)
        
        baidu_layout.addRow("API Key:", self.baidu_api_key)
        baidu_layout.addRow("Secret Key:", self.baidu_secret_key)
        
        # Qwen Settings
        qwen_group = QGroupBox("通义千问设置")
        qwen_layout = QFormLayout(qwen_group)
        
        self.qwen_api_key = QLineEdit()
        self.qwen_api_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.qwen_model = QComboBox()
        self.qwen_model.addItems(["qwen-turbo", "qwen-plus", "qwen-max"])
        
        qwen_layout.addRow("API Key:", self.qwen_api_key)
        qwen_layout.addRow("模型:", self.qwen_model)
        
        # Enabled engines
        engines_group = QGroupBox("启用的引擎")
        engines_layout = QVBoxLayout(engines_group)
        
        self.engine_checkboxes = {}
        for engine in AIEngine:
            checkbox = QCheckBox(engine.value.upper())
            self.engine_checkboxes[engine.value] = checkbox
            engines_layout.addWidget(checkbox)
        
        # Add to layout
        layout.addWidget(openai_group)
        layout.addWidget(claude_group)
        layout.addWidget(baidu_group)
        layout.addWidget(qwen_group)
        layout.addWidget(engines_group)
        layout.addStretch()
        
        return widget
    
    def _create_window_tab(self) -> QWidget:
        """Create window settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Appearance Group
        appearance_group = QGroupBox("外观设置")
        appearance_layout = QFormLayout(appearance_group)
        
        self.window_width = QSpinBox()
        self.window_width.setRange(200, 800)
        self.window_height = QSpinBox()
        self.window_height.setRange(100, 600)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(10, 24)
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(30, 100)
        self.opacity_label = QLabel("90%")
        
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        
        appearance_layout.addRow("窗口宽度:", self.window_width)
        appearance_layout.addRow("窗口高度:", self.window_height)
        appearance_layout.addRow("字体大小:", self.font_size)
        appearance_layout.addRow("透明度:", opacity_layout)
        
        # Colors Group
        colors_group = QGroupBox("颜色设置")
        colors_layout = QFormLayout(colors_group)
        
        self.bg_color = QLineEdit()
        self.text_color = QLineEdit()
        self.border_color = QLineEdit()
        
        colors_layout.addRow("背景色:", self.bg_color)
        colors_layout.addRow("文字颜色:", self.text_color)
        colors_layout.addRow("边框颜色:", self.border_color)
        
        # Add to layout
        layout.addWidget(appearance_group)
        layout.addWidget(colors_group)
        layout.addStretch()
        
        return widget
    
    def _create_behavior_tab(self) -> QWidget:
        """Create behavior settings tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Timing Group
        timing_group = QGroupBox("时间设置")
        timing_layout = QFormLayout(timing_group)
        
        self.auto_hide_delay = QSpinBox()
        self.auto_hide_delay.setRange(1000, 10000)
        self.auto_hide_delay.setSuffix(" ms")
        
        self.debounce_delay = QSpinBox()
        self.debounce_delay.setRange(100, 2000)
        self.debounce_delay.setSuffix(" ms")
        
        timing_layout.addRow("自动隐藏延迟:", self.auto_hide_delay)
        timing_layout.addRow("防抖延迟:", self.debounce_delay)
        
        # Cache Group
        cache_group = QGroupBox("缓存设置")
        cache_layout = QFormLayout(cache_group)
        
        self.enable_cache = QCheckBox("启用翻译缓存")
        self.cache_expiry = QSpinBox()
        self.cache_expiry.setRange(3600, 604800)  # 1 hour to 1 week
        self.cache_expiry.setSuffix(" 秒")
        
        cache_layout.addRow(self.enable_cache)
        cache_layout.addRow("缓存过期时间:", self.cache_expiry)
        
        # Advanced Group
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        self.max_text_length = QSpinBox()
        self.max_text_length.setRange(1000, 10000)
        
        self.max_retries = QSpinBox()
        self.max_retries.setRange(1, 10)
        
        self.timeout = QSpinBox()
        self.timeout.setRange(10, 120)
        self.timeout.setSuffix(" 秒")
        
        advanced_layout.addRow("最大文本长度:", self.max_text_length)
        advanced_layout.addRow("最大重试次数:", self.max_retries)
        advanced_layout.addRow("请求超时:", self.timeout)
        
        # Add to layout
        layout.addWidget(timing_group)
        layout.addWidget(cache_group)
        layout.addWidget(advanced_group)
        layout.addStretch()
        
        return widget
    
    def _load_settings(self) -> None:
        """Load current settings into the UI."""
        # AI Settings
        self.openai_api_key.setText(self.settings.ai.openai_api_key)
        self.openai_model.setCurrentText(self.settings.ai.openai_model)
        
        self.claude_api_key.setText(self.settings.ai.claude_api_key)
        self.claude_model.setCurrentText(self.settings.ai.claude_model)
        
        self.baidu_api_key.setText(self.settings.ai.baidu_api_key)
        self.baidu_secret_key.setText(self.settings.ai.baidu_secret_key)
        
        self.qwen_api_key.setText(self.settings.ai.qwen_api_key)
        self.qwen_model.setCurrentText(self.settings.ai.qwen_model)
        
        # Enabled engines
        for engine_name, checkbox in self.engine_checkboxes.items():
            checkbox.setChecked(engine_name in self.settings.enabled_engines)
        
        # Window Settings
        self.window_width.setValue(self.settings.window.width)
        self.window_height.setValue(self.settings.window.height)
        self.font_size.setValue(self.settings.window.font_size)
        self.opacity_slider.setValue(int(self.settings.window.opacity * 100))
        
        self.bg_color.setText(self.settings.window.background_color)
        self.text_color.setText(self.settings.window.text_color)
        self.border_color.setText(self.settings.window.border_color)
        
        # Behavior Settings
        self.auto_hide_delay.setValue(self.settings.behavior.auto_hide_delay)
        self.debounce_delay.setValue(self.settings.behavior.debounce_delay)
        self.enable_cache.setChecked(self.settings.behavior.enable_cache)
        self.cache_expiry.setValue(self.settings.behavior.cache_expiry)
        self.max_text_length.setValue(self.settings.behavior.max_text_length)
        self.max_retries.setValue(self.settings.behavior.max_retries)
        self.timeout.setValue(self.settings.behavior.timeout)
    
    def apply_settings(self) -> None:
        """Apply settings from UI to settings object."""
        try:
            # AI Settings
            self.settings.ai.openai_api_key = self.openai_api_key.text()
            self.settings.ai.openai_model = self.openai_model.currentText()
            
            self.settings.ai.claude_api_key = self.claude_api_key.text()
            self.settings.ai.claude_model = self.claude_model.currentText()
            
            self.settings.ai.baidu_api_key = self.baidu_api_key.text()
            self.settings.ai.baidu_secret_key = self.baidu_secret_key.text()
            
            self.settings.ai.qwen_api_key = self.qwen_api_key.text()
            self.settings.ai.qwen_model = self.qwen_model.currentText()
            
            # Enabled engines
            self.settings.enabled_engines = [
                engine_name for engine_name, checkbox in self.engine_checkboxes.items()
                if checkbox.isChecked()
            ]
            
            # Window Settings
            self.settings.window.width = self.window_width.value()
            self.settings.window.height = self.window_height.value()
            self.settings.window.font_size = self.font_size.value()
            self.settings.window.opacity = self.opacity_slider.value() / 100
            
            self.settings.window.background_color = self.bg_color.text()
            self.settings.window.text_color = self.text_color.text()
            self.settings.window.border_color = self.border_color.text()
            
            # Behavior Settings
            self.settings.behavior.auto_hide_delay = self.auto_hide_delay.value()
            self.settings.behavior.debounce_delay = self.debounce_delay.value()
            self.settings.behavior.enable_cache = self.enable_cache.isChecked()
            self.settings.behavior.cache_expiry = self.cache_expiry.value()
            self.settings.behavior.max_text_length = self.max_text_length.value()
            self.settings.behavior.max_retries = self.max_retries.value()
            self.settings.behavior.timeout = self.timeout.value()
            
            # Emit signal
            self.settings_changed.emit(self.settings)
            
            logger.info("Settings applied successfully")
            
        except Exception as e:
            logger.error(f"Error applying settings: {e}")
            QMessageBox.warning(self, "设置错误", f"应用设置时出错：{str(e)}")
    
    def accept(self) -> None:
        """Handle dialog acceptance."""
        self.apply_settings()
        super().accept()
    
    def reject(self) -> None:
        """Handle dialog rejection."""
        # Restore original settings
        self.settings = self.original_settings
        super().reject()