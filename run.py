#!/usr/bin/env python3
"""
启动脚本 - 桌面翻译程序
运行方式: python run.py
"""

import sys
import os

def main():
    """主启动函数，包含详细的错误处理"""
    try:
        print("🚀 启动桌面翻译程序...")

        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        print(f"✅ 项目路径已添加: {project_root}")

        # 测试基本导入
        print("🔍 测试PyQt6导入...")
        import PyQt6
        print("✅ PyQt6导入成功")

        print("🔍 测试项目模块导入...")
        from src.config import Settings
        print("✅ 配置模块导入成功")

        print("🔍 启动主程序...")
        from src.main import main as app_main
        app_main()

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保所有依赖都已安装: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
