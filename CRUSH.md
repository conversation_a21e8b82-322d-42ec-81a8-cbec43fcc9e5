# CRUSH.md - Desktop Translation Application

## Development Commands

### Running the Application
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python src/main.py

# Run with specific Python version
python3 src/main.py
```

### Testing
```bash
# Run all tests
python -m pytest tests/

# Run single test file
python -m pytest tests/test_translator.py

# Run with coverage
python -m pytest tests/ --cov=src --cov-report=html
```

### Code Quality
```bash
# Lint code
flake8 src/ --max-line-length=88 --ignore=E203,W503

# Format code
black src/ --line-length=88

# Type checking
mypy src/ --ignore-missing-imports
```

### Build and Package
```bash
# Create executable (Windows)
pyinstaller --onefile --windowed src/main.py

# Create installer (Windows)
python -c "import nsis; nsis.compile('installer.nsi')"
```

## Code Style Guidelines

### Python Style
- Follow PEP 8 with 88 character line length
- Use type hints for all function signatures
- Prefer async/await for I/O operations
- Use f-strings for string formatting
- Class names use CamelCase, variables and functions use snake_case

### Imports
- Group imports: standard library, third-party, local application
- Use absolute imports for application modules
- Avoid wildcard imports
- Use `from typing import` for type hints

### Error Handling
- Use specific exception types
- Log errors with context using the logger
- Provide user-friendly error messages
- Always clean up resources in finally blocks

### Async Patterns
- Use asyncio for all network operations
- Implement proper cancellation handling
- Use async context managers for resources
- Avoid blocking operations in async functions

### GUI Patterns
- Keep UI responsive with worker threads
- Use signals/slots for thread communication
- Implement proper cleanup in closeEvent
- Handle window state persistence

### Configuration
- Use environment variables for sensitive data
- Implement settings validation
- Provide default values for all settings
- Support runtime settings updates

### Testing
- Write unit tests for core logic
- Mock external dependencies
- Test both success and error cases
- Use fixtures for common test data