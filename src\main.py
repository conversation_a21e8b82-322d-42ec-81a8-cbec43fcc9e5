"""
Main application entry point for the desktop translation software.
"""

import asyncio
import sys
from typing import <PERSON><PERSON>
from PyQt6.QtWidgets import <PERSON>Application, QSystemTrayIcon, QMenu, QMessageBox
from PyQt6.QtCore import QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QIcon, QAction

from .config import Settings, AIEngine
from .core import ClipboardMonitor, TextProcessor, Translator, HotkeyManager
from .gui import FloatingWindow, SettingsDialog
from .utils.logger import get_logger

logger = get_logger(__name__)

class TranslationWorker(QThread):
    """Worker thread for handling translation requests."""
    
    translation_complete = pyqtSignal(str, str)  # original, translated
    
    def __init__(self, translator: Translator, text_processor: TextProcessor):
        super().__init__()
        self.translator = translator
        self.text_processor = text_processor
        self._running = True
        self._queue = []
    
    def add_translation_request(self, text: str, target_lang: str = "zh") -> None:
        """Add translation request to queue."""
        self._queue.append((text, target_lang))
    
    def run(self) -> None:
        """Main worker loop."""
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        while self._running:
            if self._queue:
                text, target_lang = self._queue.pop(0)
                
                # Process text
                processed_text, detected_lang = self.text_processor.preprocess_text(text)
                if not processed_text:
                    continue
                
                # Translate
                try:
                    logger.info(f"开始翻译: {processed_text[:30]}...")
                    # Run the async translation in this worker's event loop
                    result = loop.run_until_complete(self.translator.translate(
                        processed_text, target_lang, detected_lang
                    ))
                    
                    # Post-process
                    final_text = self.text_processor.postprocess_text(
                        result.translated_text, text
                    )
                    
                    logger.info(f"翻译工作线程发出信号: {text[:30]}... -> {final_text[:30]}...")
                    self.translation_complete.emit(text, final_text)
                
                except Exception as e:
                    logger.error(f"Translation error: {e}")
                    self.translation_complete.emit(text, f"翻译错误: {str(e)}")
            
            self.msleep(100)  # Check queue every 100ms
        
        # Close the event loop when the worker stops
        loop.close()
    
    def stop(self) -> None:
        """Stop the worker thread."""
        self._running = False

class DesktopTranslationApp:
    """Main application class."""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.settings = Settings.load()
        
        # 自动同步显示器刷新率
        self._auto_sync_display_refresh_rate()
        
        # Initialize components
        self.clipboard_monitor = ClipboardMonitor()
        self.text_processor = TextProcessor()
        self.translator = Translator(self.settings)
        self.floating_window = FloatingWindow(self.settings)
        self.settings_dialog = SettingsDialog(self.settings)
        self.hotkey_manager = HotkeyManager() # Instantiate HotkeyManager
        
        # Worker thread
        self.translation_worker = TranslationWorker(self.translator, self.text_processor)
        self.translation_worker.translation_complete.connect(
            self._on_translation_complete
        )
        self.translation_worker.start()  # 启动翻译工作线程
        
        # System tray
        self.tray_icon = None
        self._setup_system_tray()
        
        # Connect signals
        self._connect_signals()
        
        logger.info("Application initialized")
        logger.info(f"Display sync: {getattr(self.settings.behavior, 'display_sync', False)}")
        logger.info(f"Mouse follow interval: {self.settings.behavior.mouse_follow_interval}ms")
    
    def _auto_sync_display_refresh_rate(self) -> None:
        """自动检测并同步显示器刷新率"""
        try:
            if getattr(self.settings.behavior, 'display_sync', True):
                # 检测显示器刷新率
                screen = self.app.primaryScreen()
                refresh_rate = screen.refreshRate()
                ideal_interval = max(int(1000 / refresh_rate), 1)  # 最小1ms
                
                # 更新鼠标跟随间隔以匹配显示器Hz
                old_interval = self.settings.behavior.mouse_follow_interval
                self.settings.behavior.mouse_follow_interval = ideal_interval
                self.settings.behavior.mouse_follow_threshold = 2  # 2px超精细
                self.settings.behavior.auto_hide_delay = 0  # 禁用自动隐藏
                
                logger.info(f"Display auto-sync enabled: {refresh_rate}Hz -> {ideal_interval}ms interval")
                print(f"🖥️ 显示器自动同步: {refresh_rate}Hz -> {ideal_interval}ms间隔")
                print(f"⚡ 帧率提升: {1000/old_interval:.1f}FPS -> {1000/ideal_interval:.1f}FPS")
                print(f"🎯 完美匹配显示器刷新率!")
            else:
                logger.info("Display sync disabled")
        except Exception as e:
            logger.error(f"Display sync failed: {e}")
            print(f"❌ 显示器同步失败: {e}")
    
    def _setup_system_tray(self) -> None:
        """Setup system tray icon."""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            logger.warning("System tray not available")
            return
        
        # Create tray icon with default icon
        self.tray_icon = QSystemTrayIcon()
        self.tray_icon.setToolTip("桌面翻译工具")
        
        # Set a default icon (using system default application icon)
        from PyQt6.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
        from PyQt6.QtCore import QSize
        
        # Create a simple icon if none exists
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(0, 150, 255))  # 蓝色背景
        painter = QPainter(pixmap)
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(4, 4, 8, 8)  # 白色圆点
        painter.end()
        
        icon = QIcon(pixmap)
        self.tray_icon.setIcon(icon)
        
        # Create context menu
        tray_menu = QMenu()
        
        # Show/Hide action
        self.show_hide_action = QAction("显示/隐藏", self.tray_icon)
        self.show_hide_action.triggered.connect(self._toggle_window)
        tray_menu.addAction(self.show_hide_action)
        
        # Settings action
        settings_action = QAction("设置", self.tray_icon)
        settings_action.triggered.connect(self._show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("退出", self.tray_icon)
        exit_action.triggered.connect(self._quit_application)
        tray_menu.addAction(exit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        
        # Show startup message
        self.tray_icon.showMessage(
            "桌面翻译工具",
            "程序已启动，复制文本即可自动翻译",
            QSystemTrayIcon.MessageIcon.Information,
            3000
        )
    
    def _connect_signals(self) -> None:
        """Connect signals and slots."""
        # Clipboard monitor
        self.clipboard_monitor.clipboard_changed.connect(
            self._on_clipboard_changed
        )
        
        # Settings dialog
        self.settings_dialog.settings_changed.connect(
            self._on_settings_changed
        )
        
        # Floating window
        self.floating_window.translation_requested.connect(
            self._on_translation_requested
        )
        
        # Hotkey manager
        self.hotkey_manager.hotkey_triggered.connect(self._on_hotkey_triggered)
    
    def _on_hotkey_triggered(self, name: str) -> None:
        """Handle hotkey triggered event."""
        if name == "toggle_window":
            self._toggle_window()

    def _on_clipboard_changed(self, text: str) -> None:
        """Handle clipboard change event."""
        logger.info(f"剪贴板变化: {text[:50]}...")
        print(f"📋 剪贴板变化检测到: {text[:100]}...")  # 添加控制台输出便于调试

        # Add to translation queue
        self.translation_worker.add_translation_request(text)
        logger.info("翻译请求已添加到队列")
    
    def _on_translation_complete(self, original_text: str, translated_text: str) -> None:
        """Handle translation completion."""
        logger.info(f"Translation completed: {original_text[:30]}... -> {translated_text[:30]}...")
        if translated_text and translated_text != original_text:
            # 确保在主线程中更新UI
            QTimer.singleShot(0, lambda: self.floating_window.show_translation(original_text, translated_text))
    
    def _on_translation_requested(self, text: str) -> None:
        """Handle manual translation request."""
        self.translation_worker.add_translation_request(text)
    
    def _on_settings_changed(self, settings: Settings) -> None:
        """Handle settings change."""
        self.settings = settings
        
        # Update components
        self.translator.update_settings(settings)
        self.floating_window.update_settings(settings)
        self.clipboard_monitor.set_debounce_delay(settings.behavior.debounce_delay)
        self.clipboard_monitor.set_min_text_length(settings.behavior.min_text_length)
        self.clipboard_monitor.set_enable_sensitive_filter(settings.behavior.enable_sensitive_filter)
        self._setup_hotkeys() # Re-register hotkeys with new settings
        
        logger.info("Settings updated")
    
    def _setup_hotkeys(self) -> None:
        """Register hotkeys based on settings."""
        self.hotkey_manager.register_hotkey(
            "toggle_window",
            self.settings.behavior.hotkey_toggle_window
        )
        self.hotkey_manager.start() # Start listening for hotkeys
    
    def _show_settings(self) -> None:
        """Show settings dialog."""
        self.settings_dialog.show()
    
    def _toggle_window(self) -> None:
        """Toggle floating window visibility."""
        if self.floating_window.isVisible():
            self.floating_window.hide()
        else:
            self.floating_window.show()
            self.floating_window.raise_()
    
    def _quit_application(self) -> None:
        """Quit the application."""
        logger.info("Shutting down application...")
        
        # Stop components
        self.translation_worker.stop()
        self.translation_worker.wait()
        
        # Close windows
        self.floating_window.close()
        self.settings_dialog.close()
        
        # Save settings before quitting
        self.settings.save()
        logger.info("Settings saved.")
        
        # Quit application
        self.app.quit()
    
    async def _start_async_components(self) -> None:
        """Start async components."""
        # Start clipboard monitor
        await self.clipboard_monitor.start()
        
        # Start translation worker
        self.translation_worker.start()
        
        logger.info("Async components started")
    
    def run(self) -> int:
        """Run the application."""
        try:
            # Show tray icon if available
            if self.tray_icon:
                self.tray_icon.show()

            # Start hotkey manager
            self._setup_hotkeys()

            # 使用QTimer来启动异步组件，确保在主线程中运行
            from PyQt6.QtCore import QTimer

            def start_async_components():
                """启动异步组件"""
                try:
                    # 创建新的事件循环
                    import threading

                    def run_async_loop():
                        """在单独线程中运行异步事件循环"""
                        try:
                            # 创建新的事件循环
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)

                            # 运行异步组件
                            loop.run_until_complete(self._start_async_components())

                            # 保持事件循环运行
                            loop.run_forever()

                        except Exception as e:
                            logger.error(f"Async loop error: {e}")
                        finally:
                            loop.close()

                    # 启动异步线程
                    self.async_thread = threading.Thread(target=run_async_loop, daemon=True)
                    self.async_thread.start()

                    logger.info("Async components started successfully")

                except Exception as e:
                    logger.error(f"Failed to start async components: {e}")

            # 延迟启动异步组件，确保Qt应用完全初始化
            QTimer.singleShot(100, start_async_components)
            
            logger.info("Application event loop starting")
            
            # Run application
            return self.app.exec()

        except Exception as e:
            logger.error(f"Application error: {e}")
            return 1

        finally:
            # Cleanup
            self._quit_application()

def main():
    """Main entry point."""
    try:
        app = DesktopTranslationApp()
        sys.exit(app.run())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()