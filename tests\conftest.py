"""
Test configuration and fixtures.
"""

import pytest
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

@pytest.fixture
def sample_translation_result():
    """Sample translation result for testing."""
    from src.ai_engines.base_engine import TranslationResult
    
    return TranslationResult(
        original_text="Hello, world!",
        translated_text="你好，世界！",
        source_language="en",
        target_language="zh",
        engine="openai",
        confidence=0.95,
        metadata={"model": "gpt-3.5-turbo"}
    )

@pytest.fixture
def sample_settings():
    """Sample settings for testing."""
    from src.config import Settings, AISettings, WindowSettings, BehaviorSettings
    
    return Settings(
        ai=AISettings(
            openai_api_key="test_key",
            openai_model="gpt-3.5-turbo"
        ),
        window=WindowSettings(
            width=400,
            height=300,
            opacity=0.9
        ),
        behavior=BehaviorSettings(
            auto_hide_delay=3000,
            debounce_delay=500
        ),
        enabled_engines=["openai"]
    )

@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()