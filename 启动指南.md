# 桌面翻译程序 - 快速启动指南

## 🚀 快速启动

### 方法1：使用批处理文件（推荐）
双击运行 `start.bat` 文件，程序会自动检测并使用合适的Python版本启动。

### 方法2：手动启动
在命令行中运行以下命令之一：
```bash
python simple_main.py
python3 simple_main.py
py simple_main.py
```

## 📋 功能特性

### 核心功能
- ✅ **自动剪贴板监控** - 复制文本即可自动翻译
- ✅ **智能窗口定位** - 翻译窗口显示在鼠标附近
- ✅ **多AI引擎支持** - 支持OpenAI GPT系列
- ✅ **翻译缓存** - 提高翻译速度，节省API调用
- ✅ **敏感内容过滤** - 自动过滤密码、API密钥等敏感信息
- ✅ **可自定义界面** - 支持调整窗口大小、自动隐藏时间等

### 界面操作
- **拖动窗口**：鼠标左键拖动窗口标题栏
- **关闭窗口**：鼠标右键点击窗口
- **自动隐藏**：窗口在设定时间后自动隐藏
- **取消隐藏**：鼠标移入窗口时取消自动隐藏

## ⚙️ 配置说明

### 首次使用
1. 启动程序后，点击"设置"按钮
2. 在"API设置"标签页中填入您的OpenAI API密钥
3. 选择合适的模型（推荐gpt-3.5-turbo）
4. 点击"确定"保存设置

### 可配置项
- **API密钥**：您的OpenAI API密钥
- **模型选择**：gpt-3.5-turbo 或 gpt-4
- **窗口大小**：调整翻译窗口的宽度和高度
- **自动隐藏**：设置窗口自动隐藏的时间（1-10秒）
- **翻译缓存**：启用/禁用翻译结果缓存

## 🔧 故障排除

### 常见问题

**1. 程序无法启动**
- 确保已安装Python 3.7或更高版本
- 检查Python是否已添加到系统PATH环境变量
- 尝试使用不同的启动命令

**2. 翻译失败**
- 检查API密钥是否正确配置
- 确保网络连接正常
- 检查API密钥是否有足够的余额

**3. 剪贴板监控不工作**
- 确保程序有足够的权限访问剪贴板
- 尝试以管理员身份运行程序
- 检查是否有其他程序占用剪贴板

**4. 窗口显示异常**
- 调整窗口大小设置
- 重启程序
- 检查屏幕分辨率设置

### 日志文件
程序运行时会生成 `translator.log` 日志文件，包含详细的运行信息和错误信息，可用于故障排除。

## 📁 文件说明

- `simple_main.py` - 主程序文件
- `start.bat` - 启动批处理文件
- `config.json` - 配置文件（自动生成）
- `cache.json` - 翻译缓存文件（自动生成）
- `translator.log` - 日志文件（自动生成）

## 🌟 使用技巧

1. **提高翻译质量**：
   - 复制完整的句子或段落
   - 避免复制包含大量代码或格式化文本的内容

2. **节省API调用**：
   - 启用翻译缓存功能
   - 避免频繁复制相同内容

3. **提高工作效率**：
   - 调整窗口大小以适应您的屏幕
   - 设置合适的自动隐藏时间

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否符合要求
2. 网络连接是否正常
3. API密钥是否有效
4. 日志文件中的错误信息

## 🔄 更新说明

当前版本：v1.0.0
- 基于tkinter实现，无需额外依赖
- 支持OpenAI GPT系列API
- 包含基本的翻译和缓存功能
- 提供友好的用户界面