# 桌面窗口跟随翻译软件

## 项目概述
基于AI技术的智能桌面翻译工具，支持多种AI翻译引擎，实现实时剪贴板监控和智能翻译显示。

## 技术栈
- **编程语言**: Python
- **GUI框架**: PyQt6
- **AI集成**: <PERSON><PERSON><PERSON>, <PERSON>, 文心一言, 通义千问
- **系统监控**: pyperclip, keyboard
- **异步处理**: asyncio, aiohttp

## 核心功能模块
1. **系统监控层** - 剪贴板变化监听
2. **文本处理层** - 文本清理和语言检测
3. **AI翻译服务层** - 多AI引擎翻译
4. **定位跟踪层** - 鼠标位置跟踪
5. **界面展示层** - 悬浮窗显示

## 项目结构
```
桌面窗口翻译程序/
├── src/
│   ├── __init__.py
│   ├── main.py                 # 主程序入口
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # 配置管理
│   │   └── constants.py        # 常量定义
│   ├── core/
│   │   ├── __init__.py
│   │   ├── clipboard_monitor.py # 剪贴板监控
│   │   ├── text_processor.py    # 文本处理
│   │   └── translator.py        # AI翻译服务
│   ├── gui/
│   │   ├── __init__.py
│   │   ├── floating_window.py   # 悬浮窗
│   │   └── settings_dialog.py   # 设置对话框
│   ├── ai_engines/
│   │   ├── __init__.py
│   │   ├── base_engine.py      # AI引擎基类
│   │   ├── openai_engine.py    # ChatGPT
│   │   ├── claude_engine.py    # Claude
│   │   ├── baidu_engine.py     # 文心一言
│   │   └── qwen_engine.py      # 通义千问
│   └── utils/
│       ├── __init__.py
│       ├── mouse_tracker.py    # 鼠标跟踪
│       ├── cache_manager.py    # 缓存管理
│       └── logger.py           # 日志管理
├── resources/
│   ├── icons/
│   └── styles/
├── tests/
├── requirements.txt
├── README.md
└── .gitignore
```

## 开发命令
```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python src/main.py

# 运行测试
python -m pytest tests/

# 代码检查
flake8 src/
black src/
```

## 代码风格
- 遵循PEP 8规范
- 使用类型注解
- 异步编程优先
- 错误处理完善
- 模块化设计