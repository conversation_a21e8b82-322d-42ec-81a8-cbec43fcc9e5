"""
Baidu Wenxin translation engine.
"""

import asyncio
import aiohttp
import hashlib
import time
from typing import Dict, Any, Optional
from .base_engine import Base<PERSON>IEngine, TranslationResult
from ..config.constants import DEFAULT_PROMPTS, API_TIMEOUT

class BaiduEngine(BaseAIEngine):
    """Baidu Wenxin translation engine."""
    
    def __init__(self, api_key: str, secret_key: str):
        super().__init__(api_key)
        self.secret_key = secret_key
        self.base_url = "https://aip.baidubce.com"
        self.access_token = None
        self.session = None
    
    async def initialize(self) -> bool:
        """Initialize the Baidu engine."""
        if not self.is_configured():
            return False
        
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=API_TIMEOUT)
        )
        
        # Get access token
        await self._refresh_access_token()
        self._initialized = True
        return True
    
    async def _refresh_access_token(self) -> bool:
        """Refresh Baidu API access token."""
        try:
            url = f"{self.base_url}/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.secret_key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    self.access_token = data.get("access_token")
                    return bool(self.access_token)
        
        except Exception:
            pass
        
        return False
    
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh"
    ) -> TranslationResult:
        """Translate text using Baidu Wenxin."""
        if not self._validate_text(text):
            return self._create_error_result(text, "Invalid text")
        
        try:
            prompt = self._build_prompt(text, source_lang, target_lang)
            
            headers = {
                "Content-Type": "application/json"
            }
            
            payload = {
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }
            
            url = f"{self.base_url}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions"
            params = {"access_token": self.access_token}
            
            async with self.session.post(
                url, 
                headers=headers,
                params=params,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    translated_text = data.get("result", "").strip()
                    
                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        engine=self.get_engine_name(),
                        confidence=0.85,
                        metadata={
                            "usage": data.get("usage", {})
                        }
                    )
                else:
                    error_data = await response.json()
                    error_message = error_data.get("error_msg", "Unknown error")
                    return self._create_error_result(text, error_message)
        
        except asyncio.TimeoutError:
            return self._create_error_result(text, "Request timeout")
        except Exception as e:
            return self._create_error_result(text, str(e))
    
    async def detect_language(self, text: str) -> str:
        """Detect the language of the given text."""
        # For Baidu, we'll use a simple heuristic
        # In a real implementation, you might use Baidu's language detection API
        return "auto"
    
    def is_configured(self) -> bool:
        """Check if the engine is properly configured."""
        return bool(self.api_key and self.secret_key)
    
    def get_engine_name(self) -> str:
        """Get the name of the engine."""
        return "baidu"
    
    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """Build translation prompt."""
        prompt_template = DEFAULT_PROMPTS.get("baidu", "请翻译以下文本：{text}")
        
        # Customize prompt based on language pair
        if source_lang != "auto":
            prompt_template = f"请将以下文本从{source_lang}翻译成{target_lang}：\n\n{{text}}"
        
        return prompt_template.format(text=text)