"""
Claude AI translation engine.
"""

import asyncio
import aiohttp
from typing import Dict, Any, Optional
from .base_engine import <PERSON><PERSON><PERSON>ng<PERSON>, TranslationResult
from ..config.constants import DEFAULT_PROMPTS, API_TIMEOUT

class ClaudeEngine(BaseAIEngine):
    """Claude AI translation engine."""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        super().__init__(api_key)
        self.model = model
        self.base_url = "https://api.anthropic.com"
        self.session = None
    
    async def initialize(self) -> bool:
        """Initialize the Claude engine."""
        if not self.is_configured():
            return False
        
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=API_TIMEOUT)
        )
        self._initialized = True
        return True
    
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh"
    ) -> TranslationResult:
        """Translate text using Claude AI."""
        if not self._validate_text(text):
            return self._create_error_result(text, "Invalid text")
        
        try:
            prompt = self._build_prompt(text, source_lang, target_lang)
            
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            payload = {
                "model": self.model,
                "max_tokens": 2000,
                "temperature": 0.3,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }
            
            async with self.session.post(
                f"{self.base_url}/v1/messages",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    translated_text = data["content"][0]["text"].strip()
                    
                    return TranslationResult(
                        original_text=text,
                        translated_text=translated_text,
                        source_language=source_lang,
                        target_language=target_lang,
                        engine=self.get_engine_name(),
                        confidence=0.95,
                        metadata={
                            "model": self.model,
                            "usage": data.get("usage", {})
                        }
                    )
                else:
                    error_data = await response.json()
                    error_message = error_data.get("error", {}).get("message", "Unknown error")
                    return self._create_error_result(text, error_message)
        
        except asyncio.TimeoutError:
            return self._create_error_result(text, "Request timeout")
        except Exception as e:
            return self._create_error_result(text, str(e))
    
    async def detect_language(self, text: str) -> str:
        """Detect the language of the given text."""
        try:
            prompt = f"Detect the language of this text and respond with only the language code (e.g., 'en', 'zh', 'ja'): {text[:100]}"
            
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            payload = {
                "model": self.model,
                "max_tokens": 10,
                "temperature": 0.1,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }
            
            async with self.session.post(
                f"{self.base_url}/v1/messages",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data["content"][0]["text"].strip().lower()
        
        except Exception:
            pass
        
        return "auto"
    
    def is_configured(self) -> bool:
        """Check if the engine is properly configured."""
        return bool(self.api_key and self.api_key.startswith("sk-ant-"))
    
    def get_engine_name(self) -> str:
        """Get the name of the engine."""
        return "claude"
    
    def _build_prompt(self, text: str, source_lang: str, target_lang: str) -> str:
        """Build translation prompt."""
        prompt_template = DEFAULT_PROMPTS.get("claude", "Translate the following text: {text}")
        
        # Customize prompt based on language pair
        if source_lang != "auto":
            prompt_template = f"Translate from {source_lang} to {target_lang}:\n\n{{text}}"
        
        return prompt_template.format(text=text)