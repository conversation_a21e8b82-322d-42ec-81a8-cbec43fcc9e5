"""
Text processing module for translation.
"""

import re
from typing import Op<PERSON>, <PERSON><PERSON>
from ..config.constants import MAX_TEXT_LENGTH, LANGUAGE_PATTERNS
from ..utils.logger import get_logger

logger = get_logger(__name__)

class TextProcessor:
    """Processes text before and after translation."""
    
    def __init__(self):
        self.url_pattern = re.compile(r'https?://\S+|www\.\S+')
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.whitespace_pattern = re.compile(r'\s+')
        self.punctuation_pattern = re.compile(r'[^\w\s]')
    
    def preprocess_text(self, text: str) -> Tuple[str, Optional[str]]:
        """Preprocess text for translation."""
        if not text or not text.strip():
            return "", None
        
        # Clean text
        cleaned_text = self._clean_text(text)
        
        # Validate text
        validation_result = self._validate_text(cleaned_text)
        if validation_result:
            return "", validation_result
        
        # Detect language
        detected_lang = self._detect_language(cleaned_text)
        
        # Remove URLs and emails if requested
        processed_text = self._remove_urls_and_emails(cleaned_text)
        
        return processed_text, detected_lang
    
    def postprocess_text(self, translated_text: str, original_text: str) -> str:
        """Postprocess translated text."""
        if not translated_text:
            return translated_text
        
        # Restore formatting if needed
        processed_text = self._restore_formatting(translated_text, original_text)
        
        # Clean up extra whitespace
        processed_text = self._normalize_whitespace(processed_text)
        
        # Remove unnecessary backticks from translation (common with code/commands)
        processed_text = processed_text.replace('`', '') # Remove single backticks
        processed_text = processed_text.replace('```', '') # Remove triple backticks
        
        return processed_text
    
    def _clean_text(self, text: str) -> str:
        """Clean text by removing unwanted characters."""
        # Remove excessive whitespace
        text = self.whitespace_pattern.sub(' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        # Normalize line endings
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        return text
    
    def _validate_text(self, text: str) -> Optional[str]:
        """Validate text for translation."""
        # Check length
        if len(text) > MAX_TEXT_LENGTH:
            return f"Text too long (max {MAX_TEXT_LENGTH} characters)"
        
        # Check if text contains only whitespace
        if not text.strip():
            return "Empty text"
        
        # Check if text contains only special characters
        if not any(c.isalnum() for c in text):
            return "No translatable content"
        
        return None
    
    def _detect_language(self, text: str) -> Optional[str]:
        """Detect the language of the text."""
        if not text:
            return None
        
        text_sample = text[:500]  # Use first 500 characters for detection
        
        # Check for language patterns
        for lang, pattern in LANGUAGE_PATTERNS.items():
            if re.search(pattern, text_sample):
                return lang
        
        # Default to English if no specific pattern found
        if re.search(r'[a-zA-Z]', text_sample):
            return "en"
        
        return "auto"
    
    def _remove_urls_and_emails(self, text: str) -> str:
        """Remove URLs and email addresses from text."""
        # Remove URLs
        text = self.url_pattern.sub('[URL]', text)
        
        # Remove email addresses
        text = self.email_pattern.sub('[EMAIL]', text)
        
        return text
    
    def _restore_formatting(self, translated_text: str, original_text: str) -> str:
        """Restore formatting from original text to translated text more precisely."""
        if not translated_text or not original_text:
            return translated_text
        
        # Count line breaks in original text
        original_line_breaks = original_text.count('\n')
        translated_line_breaks = translated_text.count('\n')
        
        # If original has line breaks but translation doesn't, try to restore them
        if original_line_breaks > 0 and translated_line_breaks == 0:
            # Split original into segments by line breaks
            original_segments = original_text.split('\n')
            
            # If translation is much shorter, it might be a summary, so add line breaks accordingly
            if len(original_segments) > 1:
                # Try to split translation into similar number of parts
                sentences = re.split(r'[.!?。！？]', translated_text)
                sentences = [s.strip() for s in sentences if s.strip()]
                
                if len(sentences) >= len(original_segments):
                    # Reconstruct with line breaks
                    segments_per_line = len(sentences) // len(original_segments)
                    result_lines = []
                    for i in range(0, len(sentences), segments_per_line):
                        line_sentences = sentences[i:i+segments_per_line]
                        if line_sentences:
                            result_lines.append('. '.join(line_sentences) + '.')
                    translated_text = '\n'.join(result_lines)
        
        # Preserve leading/trailing whitespace patterns
        if original_text.startswith(' ') and not translated_text.startswith(' '):
            translated_text = ' ' + translated_text
        if original_text.endswith(' ') and not translated_text.endswith(' '):
            translated_text = translated_text + ' '
            
        return translated_text
    
    def _normalize_whitespace(self, text: str) -> str:
        """Normalize whitespace in text, preserving line breaks for formatting."""
        # Replace multiple spaces with a single space
        text = re.sub(r' {2,}', ' ', text)
        
        # Remove spaces before punctuation (only if punctuation is followed by non-whitespace)
        text = re.sub(r'\s+([.,;:!?)])', r'\1', text)
        
        # Add space after punctuation if missing (only if punctuation is not followed by newline)
        text = re.sub(r'([.,;:!?)])([^\s\n])', r'\1 \2', text)
        
        # Remove leading/trailing whitespace from each line individually
        # This can be complex if preserving indentation is critical.
        # For now, let's apply strip at the end for overall string.
        
        # Ensure consistent line endings (already handled in _clean_text, but as a safeguard)
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # Reduce multiple newlines to at most two (for paragraph breaks)
        text = re.sub(r'\n{3,}', '\n\n', text)

        return text.strip()
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> list:
        """Extract keywords from text."""
        # Simple keyword extraction
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Filter out common words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to',
            'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be',
            'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will',
            'would', 'could', 'should', 'may', 'might', 'must', 'can',
            '这', '那', '这', '是', '的', '了', '在', '有', '和', '就',
            '不', '人', '都', '一', '个', '上', '也', '很', '到', '说'
        }
        
        keywords = [
            word for word in words 
            if word not in stop_words and len(word) > 2
        ]
        
        # Count frequency
        word_freq = {}
        for word in keywords:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top keywords
        sorted_keywords = sorted(
            word_freq.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        return [word for word, freq in sorted_keywords[:max_keywords]]
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """Truncate text to specified length."""
        if len(text) <= max_length:
            return text
        
        # Try to truncate at word boundary
        truncated = text[:max_length]
        last_space = truncated.rfind(' ')
        
        if last_space > max_length * 0.8:
            truncated = truncated[:last_space]
        
        return truncated + "..."