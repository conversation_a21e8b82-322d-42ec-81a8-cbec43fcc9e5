"""
Base class for AI translation engines.
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
import asyncio
from dataclasses import dataclass

@dataclass
class TranslationResult:
    """Result of a translation request."""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    engine: str
    confidence: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class BaseAIEngine(ABC):
    """Base class for all AI translation engines."""
    
    def __init__(self, api_key: str, **kwargs):
        self.api_key = api_key
        self.config = kwargs
        self.session = None
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the AI engine."""
        pass
    
    @abstractmethod
    async def translate(
        self, 
        text: str, 
        source_lang: str = "auto", 
        target_lang: str = "zh"
    ) -> TranslationResult:
        """Translate text using the AI engine."""
        pass
    
    @abstractmethod
    async def detect_language(self, text: str) -> str:
        """Detect the language of the given text."""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the engine is properly configured."""
        pass
    
    @abstractmethod
    def get_engine_name(self) -> str:
        """Get the name of the engine."""
        pass
    
    async def close(self) -> None:
        """Close any open connections."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    def _validate_text(self, text: str) -> bool:
        """Validate input text."""
        if not text or not text.strip():
            return False
        return True
    
    def _create_error_result(
        self, 
        original_text: str, 
        error_message: str
    ) -> TranslationResult:
        """Create an error result."""
        return TranslationResult(
            original_text=original_text,
            translated_text=f"Error: {error_message}",
            source_language="unknown",
            target_language="unknown",
            engine=self.get_engine_name(),
            confidence=0.0,
            metadata={"error": error_message}
        )