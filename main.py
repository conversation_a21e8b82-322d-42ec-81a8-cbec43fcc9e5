#!/usr/bin/env python3
"""
桌面翻译程序 - 主启动脚本
Desktop Translation Application - Main Entry Point
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 现在可以正常导入
from src.main import DesktopTranslationApp

def main():
    """程序主入口"""
    try:
        print("🚀 启动桌面翻译程序...")
        app = DesktopTranslationApp()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("💡 请检查:")
        print("   1. 是否安装了所有依赖: pip install -r requirements.txt")
        print("   2. 是否配置了API密钥") 
        print("   3. 查看 translator.log 获取详细错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()