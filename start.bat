@echo off
echo 正在启动桌面翻译程序...

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用Python启动...
    python simple_main.py
) else (
    echo 检查其他Python版本...
    
    REM 尝试python3
    python3 --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 使用Python3启动...
        python3 simple_main.py
    ) else (
        REM 尝试py
        py --version >nul 2>&1
        if %errorlevel% equ 0 (
            echo 使用PY启动...
            py simple_main.py
        ) else (
            echo 错误：未找到Python解释器
            echo 请确保Python已安装并添加到PATH环境变量中
            echo.
            echo 或者尝试以下命令之一：
            echo   python simple_main.py
            echo   python3 simple_main.py
            echo   py simple_main.py
            pause
            exit /b 1
        )
    )
)

pause