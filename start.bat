@echo off
:: 桌面翻译程序启动脚本
chcp 65001 >nul
title 桌面翻译程序

echo.
echo ================================
echo     桌面翻译程序启动脚本
echo ================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 错误: 未检测到Python安装
        echo 💡 请先安装Python 3.7或更高版本
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo ✅ 检测到Python: 
%PYTHON_CMD% --version

:: 检查依赖是否安装
echo.
echo 🔍 检查依赖库...
%PYTHON_CMD% -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  依赖库未安装，正在安装...
    %PYTHON_CMD% -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖库检查通过
)

:: 启动程序
echo.
echo 🚀 启动程序...
echo.
%PYTHON_CMD% main.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo 💡 请检查error信息或查看translator.log文件
    pause
)