"""
简化版桌面翻译程序 - 使用tkinter实现
无需安装额外依赖，使用Python内置库
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
import sys
import time
import threading
import logging
from typing import Dict, Optional, Any
import requests
import hashlib
import tempfile
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translator.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleTranslator:
    """简化版翻译器"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.cache_file = "cache.json"
        self.config = self.load_config()
        self.cache = self.load_cache()
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        default_config = {
            "api_key": "",
            "api_url": "https://api.openai.com/v1/chat/completions",
            "model": "gpt-3.5-turbo",
            "window_width": 400,
            "window_height": 300,
            "auto_hide": 3,
            "enable_cache": True,
            "max_text_length": 1000
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    default_config.update(config)
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def load_cache(self) -> Dict[str, Any]:
        """加载缓存"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
        return {}
    
    def save_cache(self):
        """保存缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
    
    def get_cache_key(self, text: str, target_lang: str = "zh") -> str:
        """生成缓存键"""
        content = f"{text}:{target_lang}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def translate(self, text: str, target_lang: str = "zh") -> str:
        """翻译文本"""
        if not text or not text.strip():
            return ""
        
        # 检查缓存
        if self.config["enable_cache"]:
            cache_key = self.get_cache_key(text, target_lang)
            if cache_key in self.cache:
                logger.info("从缓存获取翻译")
                return self.cache[cache_key]
        
        # 检查API配置
        if not self.config["api_key"]:
            return "请先在设置中配置API密钥"
        
        # 调用翻译API
        try:
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            prompt = f"请将以下文本翻译成中文，保持原意和格式：\n\n{text}"
            
            payload = {
                "model": self.config["model"],
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.3
            }
            
            response = requests.post(
                self.config["api_url"],
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                translated_text = data["choices"][0]["message"]["content"].strip()
                
                # 缓存结果
                if self.config["enable_cache"]:
                    self.cache[cache_key] = translated_text
                    self.save_cache()
                
                return translated_text
            else:
                error_msg = f"API错误: {response.status_code}"
                logger.error(error_msg)
                return error_msg
                
        except requests.RequestException as e:
            error_msg = f"网络错误: {str(e)}"
            logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"翻译错误: {str(e)}"
            logger.error(error_msg)
            return error_msg

class TranslationWindow:
    """翻译悬浮窗口"""
    
    def __init__(self, translator: SimpleTranslator):
        self.translator = translator
        self.root = tk.Tk()
        self.root.title("翻译结果")
        self.root.overrideredirect(True)  # 无边框窗口
        self.root.attributes('-topmost', True)  # 置顶
        
        # 设置窗口样式
        self.root.configure(bg='#2b2b2b')
        
        # 创建界面元素
        self.setup_ui()
        
        # 绑定事件
        self.setup_events()
        
        # 自动隐藏定时器
        self.hide_timer = None
        
        logger.info("翻译窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 原文标签
        self.original_label = tk.Label(
            main_frame,
            text="原文: ",
            fg='#888888',
            bg='#2b2b2b',
            font=('Microsoft YaHei', 9),
            wraplength=self.translator.config["window_width"] - 40,
            justify=tk.LEFT
        )
        self.original_label.pack(fill=tk.X, pady=(0, 5))
        
        # 分隔线
        separator = tk.Frame(main_frame, height=1, bg='#555555')
        separator.pack(fill=tk.X, pady=(0, 5))
        
        # 翻译结果文本框
        self.translation_text = tk.Text(
            main_frame,
            height=8,
            width=40,
            bg='#2b2b2b',
            fg='#ffffff',
            font=('Microsoft YaHei', 10),
            wrap=tk.WORD,
            relief=tk.FLAT,
            borderwidth=0
        )
        self.translation_text.pack(fill=tk.BOTH, expand=True)
        
        # 设置窗口大小
        self.root.geometry(
            f"{self.translator.config['window_width']}x{self.translator.config['window_height']}"
        )
    
    def setup_events(self):
        """设置事件绑定"""
        # 鼠标拖动
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.on_drag)
        
        # 鼠标进入窗口时取消隐藏
        self.root.bind('<Enter>', self.cancel_hide)
        
        # 鼠标离开窗口时重新设置隐藏定时器
        self.root.bind('<Leave>', self.schedule_hide)
        
        # 右键关闭窗口
        self.root.bind('<Button-3>', lambda e: self.hide())
    
    def start_drag(self, event):
        """开始拖动"""
        self.x = event.x
        self.y = event.y
    
    def on_drag(self, event):
        """拖动窗口"""
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.root.winfo_x() + deltax
        y = self.root.winfo_y() + deltay
        self.root.geometry(f"+{x}+{y}")
    
    def show_translation(self, original_text: str, translated_text: str):
        """显示翻译结果"""
        # 更新原文
        original_preview = original_text[:100] + "..." if len(original_text) > 100 else original_text
        self.original_label.config(text=f"原文: {original_preview}")
        
        # 更新翻译结果
        self.translation_text.delete(1.0, tk.END)
        self.translation_text.insert(1.0, translated_text)
        
        # 获取鼠标位置并显示窗口
        x, y = self.root.winfo_pointerxy()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # 计算窗口位置，避免超出屏幕
        window_width = self.translator.config['window_width']
        window_height = self.translator.config['window_height']
        
        if x + window_width > screen_width:
            x = screen_width - window_width - 20
        if y + window_height > screen_height:
            y = screen_height - window_height - 20
        
        self.root.geometry(f"+{x+20}+{y+20}")
        self.root.deiconify()
        self.root.lift()
        
        # 设置自动隐藏
        self.schedule_hide()
    
    def schedule_hide(self, event=None):
        """设置自动隐藏"""
        if self.hide_timer:
            self.root.after_cancel(self.hide_timer)
        
        delay = int(self.translator.config["auto_hide"]) * 1000
        self.hide_timer = self.root.after(delay, self.hide)
    
    def cancel_hide(self, event=None):
        """取消自动隐藏"""
        if self.hide_timer:
            self.root.after_cancel(self.hide_timer)
            self.hide_timer = None
    
    def hide(self):
        """隐藏窗口"""
        self.root.withdraw()
    
    def show(self):
        """显示窗口"""
        self.root.deiconify()
        self.root.lift()

class SettingsDialog:
    """设置对话框"""
    
    def __init__(self, translator: SimpleTranslator, parent=None):
        self.translator = translator
        self.result = None
        
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.dialog.title("翻译设置")
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        
        self.setup_ui()
        self.load_config()
        
        # 模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
    
    def setup_ui(self):
        """设置用户界面"""
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # API设置标签页
        api_frame = ttk.Frame(notebook)
        notebook.add(api_frame, text="API设置")
        self.setup_api_tab(api_frame)
        
        # 窗口设置标签页
        window_frame = ttk.Frame(notebook)
        notebook.add(window_frame, text="窗口设置")
        self.setup_window_tab(window_frame)
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="确定", command=self.save_config).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.RIGHT)
    
    def setup_api_tab(self, parent):
        """设置API标签页"""
        # API密钥
        ttk.Label(parent, text="OpenAI API密钥:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_entry = ttk.Entry(parent, show="*", width=40)
        self.api_key_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 模型选择
        ttk.Label(parent, text="模型:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.model_combo = ttk.Combobox(parent, values=["gpt-3.5-turbo", "gpt-4"], width=37)
        self.model_combo.grid(row=1, column=1, padx=5, pady=5)
        
        # API URL
        ttk.Label(parent, text="API地址:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_url_entry = ttk.Entry(parent, width=40)
        self.api_url_entry.grid(row=2, column=1, padx=5, pady=5)
    
    def setup_window_tab(self, parent):
        """设置窗口标签页"""
        # 窗口宽度
        ttk.Label(parent, text="窗口宽度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.width_var = tk.IntVar(value=400)
        width_spinbox = ttk.Spinbox(parent, from_=200, to=800, textvariable=self.width_var, width=10)
        width_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 窗口高度
        ttk.Label(parent, text="窗口高度:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.height_var = tk.IntVar(value=300)
        height_spinbox = ttk.Spinbox(parent, from_=150, to=600, textvariable=self.height_var, width=10)
        height_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 自动隐藏时间
        ttk.Label(parent, text="自动隐藏(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.auto_hide_var = tk.IntVar(value=3)
        auto_hide_spinbox = ttk.Spinbox(parent, from_=1, to=10, textvariable=self.auto_hide_var, width=10)
        auto_hide_spinbox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 启用缓存
        self.cache_var = tk.BooleanVar(value=True)
        cache_check = ttk.Checkbutton(parent, text="启用翻译缓存", variable=self.cache_var)
        cache_check.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
    
    def load_config(self):
        """加载配置"""
        self.api_key_entry.insert(0, self.translator.config["api_key"])
        self.model_combo.set(self.translator.config["model"])
        self.api_url_entry.insert(0, self.translator.config["api_url"])
        self.width_var.set(self.translator.config["window_width"])
        self.height_var.set(self.translator.config["window_height"])
        self.auto_hide_var.set(self.translator.config["auto_hide"])
        self.cache_var.set(self.translator.config["enable_cache"])
    
    def save_config(self):
        """保存配置"""
        self.translator.config["api_key"] = self.api_key_entry.get()
        self.translator.config["model"] = self.model_combo.get()
        self.translator.config["api_url"] = self.api_url_entry.get()
        self.translator.config["window_width"] = self.width_var.get()
        self.translator.config["window_height"] = self.height_var.get()
        self.translator.config["auto_hide"] = self.auto_hide_var.get()
        self.translator.config["enable_cache"] = self.cache_var.get()
        
        self.translator.save_config()
        self.result = True
        self.dialog.destroy()

class ClipboardMonitor:
    """剪贴板监控器"""
    
    def __init__(self, callback):
        self.callback = callback
        self.running = False
        self.last_text = ""
        self.thread = None
    
    def start(self):
        """开始监控"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.thread.start()
            logger.info("剪贴板监控已启动")
    
    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
        logger.info("剪贴板监控已停止")
    
    def monitor_loop(self):
        """监控循环"""
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            while self.running:
                try:
                    # 获取剪贴板内容
                    clipboard_content = root.clipboard_get()
                    
                    # 检查是否有变化
                    if (clipboard_content != self.last_text and 
                        clipboard_content.strip() and
                        len(clipboard_content.strip()) > 1):
                        
                        # 过滤敏感内容
                        if not self.is_sensitive_text(clipboard_content):
                            self.last_text = clipboard_content
                            self.callback(clipboard_content)
                
                except tk.TclError:
                    # 剪贴板为空或无法访问
                    pass
                except Exception as e:
                    logger.error(f"剪贴板监控错误: {e}")
                
                time.sleep(0.5)  # 每500ms检查一次
            
            root.destroy()
            
        except Exception as e:
            logger.error(f"剪贴板监控初始化失败: {e}")
    
    def is_sensitive_text(self, text: str) -> bool:
        """检查是否为敏感文本"""
        text_lower = text.lower()
        
        # 密码相关
        if any(word in text_lower for word in ['password', 'passwd', 'pwd', 'secret', 'token', 'key']):
            return True
        
        # 信用卡号模式
        import re
        if re.search(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', text):
            return True
        
        # API密钥模式
        if any(prefix in text for prefix in ['sk-', 'pk-', 'AIza', 'ya29.']):
            return True
        
        return False

class DesktopTranslationApp:
    """桌面翻译应用程序"""
    
    def __init__(self):
        self.translator = SimpleTranslator()
        self.translation_window = TranslationWindow(self.translator)
        self.clipboard_monitor = ClipboardMonitor(self.on_clipboard_changed)
        self.setup_tray()
        
        logger.info("桌面翻译应用程序初始化完成")
    
    def setup_tray(self):
        """设置系统托盘"""
        try:
            # 创建托盘图标窗口
            self.tray_window = tk.Tk()
            self.tray_window.overrideredirect(True)
            self.tray_window.attributes('-topmost', True)
            self.tray_window.withdraw()
            
            # 创建托盘菜单
            self.create_tray_menu()
            
        except Exception as e:
            logger.error(f"系统托盘设置失败: {e}")
    
    def create_tray_menu(self):
        """创建托盘菜单"""
        # 创建菜单窗口
        self.menu_window = tk.Toplevel(self.tray_window)
        self.menu_window.overrideredirect(True)
        self.menu_window.withdraw()
        
        # 菜单项
        menu_frame = tk.Frame(self.menu_window, bg='#f0f0f0', relief=tk.RAISED, bd=1)
        menu_frame.pack()
        
        ttk.Button(menu_frame, text="显示/隐藏窗口", command=self.toggle_window).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(menu_frame, text="设置", command=self.show_settings).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(menu_frame, text="清空缓存", command=self.clear_cache).pack(fill=tk.X, padx=5, pady=2)
        ttk.Separator(menu_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, padx=5, pady=2)
        ttk.Button(menu_frame, text="退出", command=self.quit_app).pack(fill=tk.X, padx=5, pady=2)
    
    def on_clipboard_changed(self, text: str):
        """处理剪贴板变化"""
        logger.info(f"检测到剪贴板变化: {text[:50]}...")
        
        # 在新线程中进行翻译，避免阻塞UI
        threading.Thread(target=self.translate_and_show, args=(text,), daemon=True).start()
    
    def translate_and_show(self, text: str):
        """翻译并显示结果"""
        try:
            # 检查文本长度
            if len(text) > self.translator.config["max_text_length"]:
                text = text[:self.translator.config["max_text_length"]]
            
            # 翻译
            translated_text = self.translator.translate(text)
            
            # 在主线程中显示结果
            self.translation_window.show_translation(text, translated_text)
            
        except Exception as e:
            logger.error(f"翻译过程出错: {e}")
            self.translation_window.show_translation(text, f"翻译错误: {str(e)}")
    
    def toggle_window(self):
        """切换窗口显示状态"""
        if self.translation_window.root.winfo_viewable():
            self.translation_window.hide()
        else:
            self.translation_window.show()
    
    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.translator, self.translation_window.root)
        self.translation_window.root.wait_window(dialog.dialog)
        
        # 如果配置已保存，更新窗口
        if hasattr(dialog, 'result') and dialog.result:
            # 更新窗口大小
            self.translation_window.root.geometry(
                f"{self.translator.config['window_width']}x{self.translator.config['window_height']}"
            )
            messagebox.showinfo("设置", "设置已保存")
    
    def clear_cache(self):
        """清空缓存"""
        self.translator.cache.clear()
        self.translator.save_cache()
        messagebox.showinfo("缓存", "翻译缓存已清空")
    
    def quit_app(self):
        """退出应用程序"""
        logger.info("正在退出应用程序...")
        
        # 停止剪贴板监控
        self.clipboard_monitor.stop()
        
        # 关闭窗口
        self.translation_window.root.quit()
        if hasattr(self, 'tray_window'):
            self.tray_window.quit()
        
        # 退出程序
        sys.exit(0)
    
    def run(self):
        """运行应用程序"""
        try:
            # 显示启动提示
            messagebox.showinfo(
                "桌面翻译工具",
                "程序已启动！\n\n使用说明：\n1. 复制文本即可自动翻译\n2. 右键点击翻译窗口可关闭\n3. 拖动窗口可调整位置\n\n请在设置中配置API密钥"
            )
            
            # 启动剪贴板监控
            self.clipboard_monitor.start()
            
            # 运行主循环
            self.translation_window.root.mainloop()
            
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"程序运行错误: {e}")
        finally:
            self.quit_app()

def main():
    """主函数"""
    try:
        app = DesktopTranslationApp()
        app.run()
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()