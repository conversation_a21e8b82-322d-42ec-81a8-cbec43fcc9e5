"""
Test suite for the text processor module.
"""

import pytest
from src.core.text_processor import TextProcessor

class TestTextProcessor:
    """Test cases for TextProcessor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.processor = TextProcessor()
    
    def test_preprocess_text_valid(self):
        """Test text preprocessing with valid text."""
        text = "  Hello,   world!  \n\nThis is a test.  "
        processed, lang = self.processor.preprocess_text(text)
        
        assert processed == "Hello, world! This is a test."
        assert lang is not None
    
    def test_preprocess_text_empty(self):
        """Test text preprocessing with empty text."""
        text = ""
        processed, lang = self.processor.preprocess_text(text)
        
        assert processed == ""
        assert lang is None
    
    def test_preprocess_text_whitespace_only(self):
        """Test text preprocessing with whitespace only."""
        text = "   \n\n  \t  "
        processed, lang = self.processor.preprocess_text(text)
        
        assert processed == ""
        assert lang is None
    
    def test_preprocess_text_too_long(self):
        """Test text preprocessing with text that's too long."""
        text = "a" * 10000  # Longer than MAX_TEXT_LENGTH
        processed, lang = self.processor.preprocess_text(text)
        
        assert processed == ""
        assert lang is not None  # Should still detect language
    
    def test_postprocess_text(self):
        """Test text postprocessing."""
        translated = "Hello , world !"
        original = "Hello, world!"
        
        result = self.processor.postprocess_text(translated, original)
        assert result == "Hello, world!"
    
    def test_clean_text(self):
        """Test text cleaning."""
        text = "  Hello,   world!  \n\nThis is a test.  "
        cleaned = self.processor._clean_text(text)
        
        assert cleaned == "Hello, world! This is a test."
    
    def test_validate_text_valid(self):
        """Test text validation with valid text."""
        text = "Hello, world!"
        result = self.processor._validate_text(text)
        
        assert result is None
    
    def test_validate_text_empty(self):
        """Test text validation with empty text."""
        text = ""
        result = self.processor._validate_text(text)
        
        assert result == "Empty text"
    
    def test_validate_text_whitespace_only(self):
        """Test text validation with whitespace only."""
        text = "   \n\n  \t  "
        result = self.processor._validate_text(text)
        
        assert result == "Empty text"
    
    def test_validate_text_special_chars_only(self):
        """Test text validation with special characters only."""
        text = "!@#$%^&*()"
        result = self.processor._validate_text(text)
        
        assert result == "No translatable content"
    
    def test_detect_language_chinese(self):
        """Test language detection for Chinese."""
        text = "你好，世界！"
        lang = self.processor._detect_language(text)
        
        assert lang == "zh"
    
    def test_detect_language_english(self):
        """Test language detection for English."""
        text = "Hello, world!"
        lang = self.processor._detect_language(text)
        
        assert lang == "en"
    
    def test_detect_language_japanese(self):
        """Test language detection for Japanese."""
        text = "こんにちは、世界！"
        lang = self.processor._detect_language(text)
        
        assert lang == "ja"
    
    def test_detect_language_unknown(self):
        """Test language detection for unknown language."""
        text = "12345 !@#$%"
        lang = self.processor._detect_language(text)
        
        assert lang == "auto"
    
    def test_remove_urls_and_emails(self):
        """Test URL and email removal."""
        text = "Visit https://example.com <NAME_EMAIL>"
        result = self.processor._remove_urls_and_emails(text)
        
        assert result == "Visit [URL] or email [EMAIL]"
    
    def test_normalize_whitespace(self):
        """Test whitespace normalization."""
        text = "Hello,   world!  This  is  a  test."
        result = self.processor._normalize_whitespace(text)
        
        assert result == "Hello, world! This is a test."
    
    def test_extract_keywords(self):
        """Test keyword extraction."""
        text = "The quick brown fox jumps over the lazy dog. The fox was very quick."
        keywords = self.processor.extract_keywords(text, max_keywords=5)
        
        assert "quick" in keywords
        assert "fox" in keywords
        assert len(keywords) <= 5
    
    def test_truncate_text_short(self):
        """Test text truncation with short text."""
        text = "Hello, world!"
        result = self.processor.truncate_text(text, 50)
        
        assert result == text
    
    def test_truncate_text_long(self):
        """Test text truncation with long text."""
        text = "This is a very long text that should be truncated."
        result = self.processor.truncate_text(text, 20)
        
        assert len(result) <= 23  # 20 + "..."
        assert result.endswith("...")
    
    def test_restore_formatting_with_line_breaks(self):
        """Test formatting restoration with line breaks."""
        translated = "First sentence. Second sentence. Third sentence."
        original = "First sentence.\nSecond sentence.\nThird sentence."
        
        result = self.processor._restore_formatting(translated, original)
        assert "\n" in result