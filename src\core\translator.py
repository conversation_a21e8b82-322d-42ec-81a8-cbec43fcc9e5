"""
Main translation service module.
"""

import asyncio
from typing import Dict, Optional, List
from ..ai_engines import (
    BaseAIEngine, OpenAIEngine, ClaudeE<PERSON>ine, BaiduEngine, QwenEngine
)
from ..ai_engines.base_engine import TranslationResult
from ..config import Settings, AIEngine
from ..utils.cache_manager import CacheManager
from ..utils.logger import get_logger

logger = get_logger(__name__)

class Translator:
    """Main translation service that manages multiple AI engines."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.engines: Dict[str, BaseAIEngine] = {}
        self.cache_manager = CacheManager(
            expiry_time=settings.behavior.cache_expiry
        )
        # Engines are now lazily loaded
    
    def _load_engine(self, engine_name: str) -> Optional[BaseAIEngine]:
        """Lazily load and return a specific AI engine by name."""
        if engine_name in self.engines:
            return self.engines[engine_name]

        engine = None
        if engine_name == AIEngine.OPENAI.value:
            if self.settings.ai.openai_api_key:
                engine = OpenAIEngine(
                    self.settings.ai.openai_api_key,
                    self.settings.ai.openai_model
                )
                logger.info("Loaded OpenAI engine")
        elif engine_name == AIEngine.CLAUDE.value:
            if self.settings.ai.claude_api_key:
                engine = ClaudeEngine(
                    self.settings.ai.claude_api_key,
                    self.settings.ai.claude_model
                )
                logger.info("Loaded Claude engine")
        elif engine_name == AIEngine.BAIDU.value:
            if self.settings.ai.baidu_api_key and self.settings.ai.baidu_secret_key:
                engine = BaiduEngine(
                    self.settings.ai.baidu_api_key,
                    self.settings.ai.baidu_secret_key
                )
                logger.info("Loaded Baidu engine")
        elif engine_name == AIEngine.QWEN.value:
            if self.settings.ai.qwen_api_key:
                engine = QwenEngine(
                    self.settings.ai.qwen_api_key,
                    self.settings.ai.qwen_model
                )
                logger.info("Loaded Qwen engine")
        elif engine_name == "custom": # Handle custom API separately as it reuses OpenAIEngine
            if hasattr(self.settings.ai, 'custom_api_key') and self.settings.ai.custom_api_key:
                try:
                    custom_name = getattr(self.settings.ai, 'custom_name', 'Custom API')
                    custom_engine = OpenAIEngine(
                        self.settings.ai.custom_api_key,
                        self.settings.ai.custom_model,
                        custom_name=custom_name
                    )
                    if hasattr(self.settings.ai, 'custom_base_url') and self.settings.ai.custom_base_url:
                        custom_engine.base_url = self.settings.ai.custom_base_url
                    engine = custom_engine
                    logger.info(f"Loaded custom API: {custom_name}")
                except Exception as e:
                    logger.error(f"Failed to load custom API: {e}")

        if engine:
            self.engines[engine_name] = engine
        return engine

    async def translate(
        self, 
        text: str, 
        target_lang: str = "zh",
        source_lang: str = "auto",
        engine: Optional[str] = None
    ) -> TranslationResult:
        """Translate text using specified or best available engine."""
        if not text or not text.strip():
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                engine="none",
                confidence=0.0,
                metadata={"error": "Empty text"}
            )
        
        # Check cache first
        if self.settings.behavior.enable_cache:
            cached_result = self.cache_manager.get(
                text, engine or "auto", target_lang
            )
            if cached_result:
                logger.info("Translation found in cache")
                return cached_result
        
        # Select engine
        selected_engine_name = engine or self.settings.ai.default_engine
        selected_engine = self._load_engine(selected_engine_name) # Ensure engine is loaded
        
        if not selected_engine:
            logger.warning(f"No AI engine available or configured for {selected_engine_name}")
            return TranslationResult(
                original_text=text,
                translated_text="No AI engine available",
                source_language=source_lang,
                target_language=target_lang,
                engine="none",
                confidence=0.0,
                metadata={"error": "No configured AI engines"}
            )
        
        # Perform translation
        try:
            async with selected_engine:
                result = await selected_engine.translate(
                    text, source_lang, target_lang
                )
                
                # Cache successful translations
                if (self.settings.behavior.enable_cache and 
                    result.confidence > 0.5):
                    self.cache_manager.set(result)
                
                logger.info(f"Translation completed using {result.engine}")
                return result
        
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return TranslationResult(
                original_text=text,
                translated_text=f"Translation error: {str(e)}",
                source_language=source_lang,
                target_language=target_lang,
                engine=selected_engine.get_engine_name(),
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _select_engine(self, preferred_engine: Optional[str]) -> Optional[BaseAIEngine]:
        """Select the best available engine, loading it if necessary."""
        # Try preferred engine first
        if preferred_engine:
            engine = self._load_engine(preferred_engine)
            if engine: return engine
        
        # Try default engine from settings
        default_engine_name = self.settings.ai.default_engine
        if default_engine_name:
            engine = self._load_engine(default_engine_name)
            if engine: return engine

        # Try enabled engines in order
        if self.settings.enabled_engines:
            for engine_name in self.settings.enabled_engines:
                engine = self._load_engine(engine_name)
                if engine: return engine
        
        # Fall back to any engine already loaded (should not happen often with lazy loading)
        if self.engines:
            return next(iter(self.engines.values()))
        
        return None
    
    async def detect_language(self, text: str, engine: Optional[str] = None) -> str:
        """Detect language of text using specified engine."""
        selected_engine = self._select_engine(engine)
        if not selected_engine:
            return "auto"
        
        try:
            async with selected_engine:
                return await selected_engine.detect_language(text)
        except Exception as e:
            logger.error(f"Language detection error: {e}")
            return "auto"
    
    def get_available_engines(self) -> List[str]:
        """Get list of available engine names based on configured settings."""
        configured_engines = []
        if self.settings.ai.openai_api_key: configured_engines.append(AIEngine.OPENAI.value)
        if self.settings.ai.claude_api_key: configured_engines.append(AIEngine.CLAUDE.value)
        if self.settings.ai.baidu_api_key and self.settings.ai.baidu_secret_key: configured_engines.append(AIEngine.BAIDU.value)
        if self.settings.ai.qwen_api_key: configured_engines.append(AIEngine.QWEN.value)
        if hasattr(self.settings.ai, 'custom_api_key') and self.settings.ai.custom_api_key: configured_engines.append("custom")
        return configured_engines
    
    def is_engine_configured(self, engine_name: str) -> bool:
        """Check if an engine is configured (has API keys etc.)."""
        if engine_name == AIEngine.OPENAI.value: return bool(self.settings.ai.openai_api_key)
        if engine_name == AIEngine.CLAUDE.value: return bool(self.settings.ai.claude_api_key)
        if engine_name == AIEngine.BAIDU.value: return bool(self.settings.ai.baidu_api_key and self.settings.ai.baidu_secret_key)
        if engine_name == AIEngine.QWEN.value: return bool(self.settings.ai.qwen_api_key)
        if engine_name == "custom": return bool(hasattr(self.settings.ai, 'custom_api_key') and self.settings.ai.custom_api_key)
        return False
    
    async def test_engine(self, engine_name: str) -> bool:
        """Test if an engine is working."""
        if engine_name not in self.engines:
            return False
        
        engine = self.engines[engine_name]
        try:
            async with engine:
                return await engine.initialize()
        except Exception as e:
            logger.error(f"Engine test failed for {engine_name}: {e}")
            return False
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics."""
        return self.cache_manager.get_stats()
    
    def clear_cache(self) -> None:
        """Clear translation cache."""
        self.cache_manager.clear()
        logger.info("Translation cache cleared")
    
    def update_settings(self, new_settings: Settings) -> None:
        """Update translator settings."""
        old_settings = self.settings
        self.settings = new_settings

        # Update cache settings immediately
        self.cache_manager.expiry_time = new_settings.behavior.cache_expiry

        # Re-evaluate which engines need to be reloaded/reinitialized
        # This is a simplified approach. A more robust solution might compare
        # specific API keys/models to avoid unnecessary reloads.
        # For now, we'll clear and re-load any engines that are currently loaded
        # if their corresponding settings might have changed.

        # Collect all engine names that might be affected by settings change
        affected_engine_names = set(self.engines.keys())
        # Add default and enabled engines to the set, as they might be new or changed
        affected_engine_names.add(new_settings.ai.default_engine)
        if new_settings.enabled_engines:
            affected_engine_names.update(new_settings.enabled_engines)

        for engine_name in list(affected_engine_names):
            # Close and remove existing engine if its settings might have changed
            if engine_name in self.engines:
                # A simple heuristic: if any AI setting changed, assume all need reload
                # A more precise check would be comparing old_settings.ai vs new_settings.ai for specific engine
                if old_settings.ai != new_settings.ai: # simplified check
                    asyncio.create_task(self.engines[engine_name].close())
                    del self.engines[engine_name]
                    # Re-load the engine to reflect new settings
                    self._load_engine(engine_name) # This will re-initialize if needed
            else:
                # If engine was not loaded, but is now configured/enabled, load it
                self._load_engine(engine_name)

        logger.info("Translator settings updated and engines re-evaluated")