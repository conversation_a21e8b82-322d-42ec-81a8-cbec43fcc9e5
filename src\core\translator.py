"""
Main translation service module.
"""

import asyncio
from typing import Dict, Optional, List
from ..ai_engines import (
    BaseAIEngine, OpenAIEngine, ClaudeEngine, BaiduEngine, QwenEngine
)
from ..ai_engines.base_engine import TranslationResult
from ..config import Settings, AIEngine
from ..utils.cache_manager import CacheManager
from ..utils.logger import get_logger

logger = get_logger(__name__)

class Translator:
    """Main translation service that manages multiple AI engines."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.engines: Dict[str, BaseAIEngine] = {}
        self.cache_manager = CacheManager(
            expiry_time=settings.behavior.cache_expiry
        )
        self._initialize_engines()
    
    def _initialize_engines(self) -> None:
        """Initialize available AI engines."""
        # Initialize OpenAI engine (原版)
        if self.settings.ai.openai_api_key and not hasattr(self.settings.ai, 'custom_api_key'):
            self.engines[AIEngine.OPENAI.value] = OpenAIEngine(
                self.settings.ai.openai_api_key,
                self.settings.ai.openai_model
            )
        
        # Initialize Third-party API (使用OpenAI兼容接口)
        if hasattr(self.settings.ai, 'custom_api_key') and self.settings.ai.custom_api_key:
            try:
                # 创建自定义OpenAI引擎，使用第三方API
                custom_engine = OpenAIEngine(
                    self.settings.ai.custom_api_key,
                    self.settings.ai.custom_model
                )
                # 设置自定义API URL
                if hasattr(self.settings.ai, 'custom_base_url') and self.settings.ai.custom_base_url:
                    custom_engine.base_url = self.settings.ai.custom_base_url
                
                self.engines['custom'] = custom_engine
                # 同时也注册为openai，这样translate方法可以找到
                self.engines[AIEngine.OPENAI.value] = custom_engine
                logger.info(f"Initialized custom API: {getattr(self.settings.ai, 'custom_name', 'Unknown')}")
            except Exception as e:
                logger.error(f"Failed to initialize custom API: {e}")
        
        # Initialize Claude engine
        if self.settings.ai.claude_api_key:
            self.engines[AIEngine.CLAUDE.value] = ClaudeEngine(
                self.settings.ai.claude_api_key,
                self.settings.ai.claude_model
            )
        
        # Initialize Baidu engine
        if self.settings.ai.baidu_api_key and self.settings.ai.baidu_secret_key:
            self.engines[AIEngine.BAIDU.value] = BaiduEngine(
                self.settings.ai.baidu_api_key,
                self.settings.ai.baidu_secret_key
            )
        
        # Initialize Qwen engine
        if self.settings.ai.qwen_api_key:
            self.engines[AIEngine.QWEN.value] = QwenEngine(
                self.settings.ai.qwen_api_key,
                self.settings.ai.qwen_model
            )
        
        logger.info(f"Initialized {len(self.engines)} AI engines")
    
    async def translate(
        self, 
        text: str, 
        target_lang: str = "zh",
        source_lang: str = "auto",
        engine: Optional[str] = None
    ) -> TranslationResult:
        """Translate text using specified or best available engine."""
        if not text or not text.strip():
            return TranslationResult(
                original_text=text,
                translated_text="",
                source_language=source_lang,
                target_language=target_lang,
                engine="none",
                confidence=0.0,
                metadata={"error": "Empty text"}
            )
        
        # Check cache first
        if self.settings.behavior.enable_cache:
            cached_result = self.cache_manager.get(
                text, engine or "auto", target_lang
            )
            if cached_result:
                logger.info("Translation found in cache")
                return cached_result
        
        # Select engine
        selected_engine = self._select_engine(engine)
        if not selected_engine:
            return TranslationResult(
                original_text=text,
                translated_text="No AI engine available",
                source_language=source_lang,
                target_language=target_lang,
                engine="none",
                confidence=0.0,
                metadata={"error": "No configured AI engines"}
            )
        
        # Perform translation
        try:
            async with selected_engine:
                result = await selected_engine.translate(
                    text, source_lang, target_lang
                )
                
                # Cache successful translations
                if (self.settings.behavior.enable_cache and 
                    result.confidence > 0.5):
                    self.cache_manager.set(result)
                
                logger.info(f"Translation completed using {result.engine}")
                return result
        
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return TranslationResult(
                original_text=text,
                translated_text=f"Translation error: {str(e)}",
                source_language=source_lang,
                target_language=target_lang,
                engine=selected_engine.get_engine_name(),
                confidence=0.0,
                metadata={"error": str(e)}
            )
    
    def _select_engine(self, preferred_engine: Optional[str]) -> Optional[BaseAIEngine]:
        """Select the best available engine."""
        if preferred_engine and preferred_engine in self.engines:
            return self.engines[preferred_engine]
        
        # Try enabled engines in order
        for engine_name in self.settings.enabled_engines:
            if engine_name in self.engines:
                return self.engines[engine_name]
        
        # Fall back to any available engine
        if self.engines:
            return next(iter(self.engines.values()))
        
        return None
    
    async def detect_language(self, text: str, engine: Optional[str] = None) -> str:
        """Detect language of text using specified engine."""
        selected_engine = self._select_engine(engine)
        if not selected_engine:
            return "auto"
        
        try:
            async with selected_engine:
                return await selected_engine.detect_language(text)
        except Exception as e:
            logger.error(f"Language detection error: {e}")
            return "auto"
    
    def get_available_engines(self) -> List[str]:
        """Get list of available engine names."""
        return list(self.engines.keys())
    
    def is_engine_configured(self, engine_name: str) -> bool:
        """Check if an engine is configured."""
        return engine_name in self.engines and self.engines[engine_name].is_configured()
    
    async def test_engine(self, engine_name: str) -> bool:
        """Test if an engine is working."""
        if engine_name not in self.engines:
            return False
        
        engine = self.engines[engine_name]
        try:
            async with engine:
                return await engine.initialize()
        except Exception as e:
            logger.error(f"Engine test failed for {engine_name}: {e}")
            return False
    
    def get_cache_stats(self) -> Dict:
        """Get cache statistics."""
        return self.cache_manager.get_stats()
    
    def clear_cache(self) -> None:
        """Clear translation cache."""
        self.cache_manager.clear()
        logger.info("Translation cache cleared")
    
    def update_settings(self, settings: Settings) -> None:
        """Update translator settings."""
        self.settings = settings
        
        # Reinitialize engines with new settings
        for engine in self.engines.values():
            asyncio.create_task(engine.close())
        
        self.engines.clear()
        self._initialize_engines()
        
        # Update cache settings
        self.cache_manager.expiry_time = settings.behavior.cache_expiry
        
        logger.info("Translator settings updated")