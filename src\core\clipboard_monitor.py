"""
Clipboard monitoring module.
"""

import asyncio
import time
import pyperclip
from typing import Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal
from ..config.constants import DEBOUNCE_DELAY
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ClipboardMonitor(QObject):
    """Monitors clipboard changes and triggers translation."""
    
    clipboard_changed = pyqtSignal(str)  # Signal emitted when clipboard changes
    
    def __init__(self):
        super().__init__()
        self._running = False
        self._last_text = ""
        self._last_change_time = 0
        self._debounce_delay = DEBOUNCE_DELAY
        self._monitor_task = None
        self._callbacks = []
    
    def add_callback(self, callback: Callable[[str], None]) -> None:
        """Add callback function to be called when clipboard changes."""
        self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[str], None]) -> None:
        """Remove callback function."""
        if callback in self._callbacks:
            self._callbacks.remove(callback)
    
    async def start(self) -> None:
        """Start monitoring clipboard."""
        if self._running:
            return
        
        self._running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Clipboard monitor started")
    
    async def stop(self) -> None:
        """Stop monitoring clipboard."""
        if not self._running:
            return
        
        self._running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Clipboard monitor stopped")
    
    async def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self._running:
            try:
                current_text = pyperclip.paste()
                
                # Check if clipboard content has changed
                if (current_text != self._last_text and 
                    current_text.strip() and
                    self._should_process_change(current_text)):
                    
                    self._last_text = current_text
                    self._last_change_time = time.time()
                    
                    # Emit signal and call callbacks
                    self.clipboard_changed.emit(current_text)
                    for callback in self._callbacks:
                        try:
                            callback(current_text)
                        except Exception as e:
                            logger.error(f"Error in clipboard callback: {e}")
                
                await asyncio.sleep(0.1)  # Check every 100ms
            
            except Exception as e:
                logger.error(f"Error in clipboard monitor: {e}")
                await asyncio.sleep(1)  # Wait before retrying
    
    def _should_process_change(self, text: str) -> bool:
        """Check if the clipboard change should be processed."""
        current_time = time.time()
        
        # Debounce - ignore rapid changes
        if current_time - self._last_change_time < self._debounce_delay / 1000:
            return False
        
        # Ignore empty or whitespace-only text
        if not text or not text.strip():
            return False
        
        # Ignore very short text (likely accidental)
        if len(text.strip()) < 2:
            return False
        
        # Ignore text that looks like a password or sensitive data
        if self._is_sensitive_text(text):
            return False
        
        return True
    
    def _is_sensitive_text(self, text: str) -> bool:
        """Check if text might contain sensitive information."""
        text_lower = text.lower()
        
        # Common password indicators
        if any(indicator in text_lower for indicator in [
            'password', 'passwd', 'pwd', 'secret', 'token', 'key'
        ]):
            return True
        
        # Credit card pattern
        if len(text.replace(' ', '').replace('-', '')) == 16 and text.replace(' ', '').replace('-', '').isdigit():
            return True
        
        # API key patterns
        if any(prefix in text for prefix in [
            'sk-', 'pk-', 'AIza', 'ya29.', 'ghp_', 'gho_', 'ghu_', 'ghs_', 'ghr_'
        ]):
            return True
        
        return False
    
    def set_debounce_delay(self, delay_ms: int) -> None:
        """Set debounce delay in milliseconds."""
        self._debounce_delay = delay_ms
    
    def get_last_text(self) -> str:
        """Get the last detected clipboard text."""
        return self._last_text