"""
Clipboard monitoring module.
"""

import time
import pyperclip
from typing import Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from ..config.constants import DEBOUNCE_DELAY
from ..utils.logger import get_logger

logger = get_logger(__name__)

class ClipboardMonitor(QObject):
    """Monitors clipboard changes and triggers translation."""
    
    clipboard_changed = pyqtSignal(str)  # Signal emitted when clipboard changes
    
    def __init__(self):
        super().__init__()
        self._running = False
        self._last_text = ""
        self._last_change_time = 0
        self._debounce_delay = DEBOUNCE_DELAY
        self._timer = QTimer()
        self._timer.timeout.connect(self._check_clipboard)
        self._callbacks = []
        self._min_text_length = 1 # Default value
        self._enable_sensitive_filter = True # Default value
    
    def add_callback(self, callback: Callable[[str], None]) -> None:
        """Add callback function to be called when clipboard changes."""
        self._callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[str], None]) -> None:
        """Remove callback function."""
        if callback in self._callbacks:
            self._callbacks.remove(callback)
    
    def start(self) -> None:
        """Start monitoring clipboard."""
        if self._running:
            return
        
        self._running = True
        self._timer.start(100)  # Check every 100ms
        logger.info("Clipboard monitor started")
    
    def stop(self) -> None:
        """Stop monitoring clipboard."""
        if not self._running:
            return
        
        self._running = False
        self._timer.stop()
        logger.info("Clipboard monitor stopped")
    
    def _check_clipboard(self) -> None:
        """Check clipboard for changes."""
        if not self._running:
            return
            
        try:
            current_text = pyperclip.paste()
            
            # Check if clipboard content has changed
            if (current_text != self._last_text and 
                current_text.strip() and
                self._should_process_change(current_text)):
                
                self._last_text = current_text
                self._last_change_time = time.time()
                
                logger.info(f"剪贴板变化检测: {current_text[:50]}...")
                
                # Emit signal and call callbacks
                self.clipboard_changed.emit(current_text)
                for callback in self._callbacks:
                    try:
                        callback(current_text)
                    except Exception as e:
                        logger.error(f"Error in clipboard callback: {e}")
        
        except Exception as e:
            logger.error(f"Error in clipboard monitor: {e}")
            # 继续运行，不要停止定时器
    
    def _should_process_change(self, text: str) -> bool:
        """Check if the clipboard change should be processed."""
        current_time = time.time()
        
        # Debounce - ignore rapid changes
        if current_time - self._last_change_time < self._debounce_delay / 1000:
            return False
        
        # Ignore empty or whitespace-only text
        if not text or not text.strip():
            return False
        
        # Ignore very short text (likely accidental), if enabled
        if len(text.strip()) < self._min_text_length:
            return False
        
        # Ignore text that looks like a password or sensitive data, if enabled
        if self._enable_sensitive_filter and self._is_sensitive_text(text):
            return False
        
        return True
    
    def _is_sensitive_text(self, text: str) -> bool:
        
        text_lower = text.lower()
        
        # Common password indicators
        if any(indicator in text_lower for indicator in [
            'password', 'passwd', 'pwd', 'secret', 'token', 'key'
        ]):
            return True
        
        # Credit card pattern
        if len(text.replace(' ', '').replace('-', '')) == 16 and text.replace(' ', '').replace('-', '').isdigit():
            return True
        
        # API key patterns
        if any(prefix in text for prefix in [
            'sk-', 'pk-', 'AIza', 'ya29.', 'ghp_', 'gho_', 'ghu_', 'ghs_', 'ghr_'
        ]):
            return True
        
        return False # Ensure a return value if no sensitive text is found
    
    def set_debounce_delay(self, delay_ms: int) -> None:
        """Set debounce delay in milliseconds."""
        self._debounce_delay = delay_ms
    
    def set_min_text_length(self, length: int) -> None:
        """Set minimum text length for processing."""
        self._min_text_length = length

    def set_enable_sensitive_filter(self, enable: bool) -> None:
        """Enable or disable sensitive text filtering."""
        self._enable_sensitive_filter = enable

    def get_last_text(self) -> str:
        """Get the last detected clipboard text."""
        return self._last_text