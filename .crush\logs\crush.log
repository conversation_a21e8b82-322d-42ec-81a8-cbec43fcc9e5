{"time":"2025-08-02T16:21:21.4238706+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-02T16:21:21.452398+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-02T16:21:22.109583+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (1.04ms)"}
{"time":"2025-08-02T16:21:22.1101033+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (520.3µs)"}
{"time":"2025-08-02T16:21:22.1106129+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (509.6µs)"}
{"time":"2025-08-02T16:21:22.1111665+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (553.6µs)"}
{"time":"2025-08-02T16:21:22.1111665+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-02T16:21:22.1116891+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-02T16:21:22.1148179+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-02T16:21:22.1153379+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-02T16:21:22.1163839+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-02T16:21:22.1163839+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-02T16:21:22.1795991+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-02T16:21:25.6100662+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-02T16:21:25.6100662+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-02T16:21:25.6116202+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-02T16:21:25.6116202+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-02T16:21:27.7383561+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_h5z360xdy7r","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:27.7563184+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_h5z360xdy7r","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:28.8385364+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_sm84nlnqini","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:28.8395772+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_sm84nlnqini","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:29.8799678+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_arykygonuzi","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:29.8805074+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_arykygonuzi","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:30.529113+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_0kblcorqvhv","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:30.5349815+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_0kblcorqvhv","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:30.5656391+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/tools.init.func1","file":"/home/<USER>/work/crush/crush/internal/llm/tools/rg.go","line":18},"msg":"Ripgrep (rg) not found in $PATH. Some grep features might be limited or slower."}
{"time":"2025-08-02T16:21:31.4511688+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_lq1gg15c8kj","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:31.4528458+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_lq1gg15c8kj","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:32.4037488+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_344g1eqskbq","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:32.4053151+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_344g1eqskbq","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:33.9510374+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"dir /b \"D:\\AIGC-dm\\桌面窗口翻译程序\"","err":"exit status 127"}
{"time":"2025-08-02T16:21:35.0494517+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_k2hnexo96","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:35.0510316+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_k2hnexo96","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:35.0532751+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"ls -la \"D:\\AIGC-dm\\桌面窗口翻译程序\"","err":null}
{"time":"2025-08-02T16:21:35.9333254+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_o8wzol2xv7p","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:35.934424+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_o8wzol2xv7p","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:36.5491428+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_mzcs6e4zv1m","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:36.5502054+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_mzcs6e4zv1m","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:37.512985+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_e2ogc1m1ex","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:37.5162369+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_e2ogc1m1ex","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:46.6258854+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_26s1tv7fq4a","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:46.6279874+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_26s1tv7fq4a","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:47.997181+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_gsx11qopajv","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:47.9987598+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_gsx11qopajv","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:48.6692387+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_9sh1rbfd1w","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:48.6703627+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_9sh1rbfd1w","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:49.4039999+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_08qn1k9cbql3","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:49.4055083+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_08qn1k9cbql3","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:50.1357541+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_18tc3p0riyw","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:50.1367968+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_18tc3p0riyw","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:50.6934538+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_rwal7bfjq","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:50.6955498+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_rwal7bfjq","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:52.1275625+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_lmt5ory0wz","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:52.1286049+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_lmt5ory0wz","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:52.9543562+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_p12qnzlmaj8","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:52.9564643+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_p12qnzlmaj8","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:53.4857933+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_p6k86zl9lv","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:53.4868873+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_p6k86zl9lv","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:54.0517717+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_vm1ibci50h9","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:54.0528306+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_vm1ibci50h9","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:54.6318964+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_xgohre7hiri","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:54.6329537+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_xgohre7hiri","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:55.4473055+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_c5p1shujr2m","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:55.4483776+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_c5p1shujr2m","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:56.055404+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_zz47i2v375l","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:56.057001+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_zz47i2v375l","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:56.8620407+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_4mm3pzd48np","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:56.8646612+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_4mm3pzd48np","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:59.2105455+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_v78iiw0qd2n","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:59.2110504+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_v78iiw0qd2n","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:59.8727922+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_k9g4gzorte","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:21:59.8749005+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_k9g4gzorte","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:00.9522835+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_hwcsq01j9c","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:00.9548971+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_hwcsq01j9c","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:01.6642203+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_iqwvcgplvb","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:01.666302+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_iqwvcgplvb","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:02.4922257+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_2vjufkvjx9n","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:02.4933639+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_2vjufkvjx9n","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:03.1045539+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_2dj0ffbj0ly","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:03.1055559+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_2dj0ffbj0ly","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:05.8489672+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_1lszqumz4xu","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:05.8510494+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_1lszqumz4xu","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:08.428812+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_wis6cd1jzbl","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:08.4298997+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_wis6cd1jzbl","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:09.0503287+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_vau1h2ymwsj","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:09.0514675+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_vau1h2ymwsj","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:19.9148566+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_xww7zpkf0mp","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:19.9164227+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_xww7zpkf0mp","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:21.275967+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_u82hcvpzfl","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:21.27659+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_u82hcvpzfl","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:21.969796+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_3wkz9xfs0tu","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:21.9713606+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_3wkz9xfs0tu","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:22.6888027+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_txl5x6ry6v8","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:22.6893182+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_txl5x6ry6v8","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:23.5815677+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_zcgl7kfbkh8","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:23.5826447+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_zcgl7kfbkh8","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:24.3699687+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_znnqavg1uno","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:24.3704727+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_znnqavg1uno","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:24.9081707+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_nqcl603vbbg","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:24.9092255+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_nqcl603vbbg","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:25.6143504+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_mywhga7gdp","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:25.6150852+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_mywhga7gdp","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:26.1836467+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_f2mawij0fb","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:26.1841517+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_f2mawij0fb","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:26.7635714+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_6877kvm8g5e","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:26.7640809+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_6877kvm8g5e","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:27.5439119+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_mfwm1e9n87i","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:27.5449512+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_mfwm1e9n87i","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:28.2366146+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_v8rqpmksufl","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:28.237671+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_v8rqpmksufl","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:28.7630116+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_hnapabsmmk6","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:28.7640427+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_hnapabsmmk6","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:29.4008859+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_n9abha9e8hf","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:29.4030132+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_n9abha9e8hf","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:30.6495363+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_09yywctac95k","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:30.6521334+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_09yywctac95k","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:31.4026952+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_e0bte5uwa9u","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:31.4037463+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_e0bte5uwa9u","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:32.1493104+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_k67i5mg1vu","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:32.1503974+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_k67i5mg1vu","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:32.7149525+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_fjtek55n0zc","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:32.7155174+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_fjtek55n0zc","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:33.3857536+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_0a8oaw0qernc","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:33.3878482+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_0a8oaw0qernc","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:34.1007955+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_wdn9sl7bym9","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:34.1018606+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_wdn9sl7bym9","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:35.0329491+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_klj65x2hmit","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:35.0334533+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_klj65x2hmit","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:35.6326091+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_vlk3aja0ku","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:35.6336302+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_vlk3aja0ku","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:36.1999011+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_quk46fe137c","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:36.2009437+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_quk46fe137c","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:37.1884749+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_6e15ujubasm","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:37.1900433+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_6e15ujubasm","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:37.8738684+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_3dy2kxgwjm4","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:37.8749118+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_3dy2kxgwjm4","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:38.7138185+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_ys4lt2z6vvd","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:38.7144216+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_ys4lt2z6vvd","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:39.3471347+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_8lvjl1n1vag","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:39.3481757+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_8lvjl1n1vag","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:40.9584344+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_oydsee6qg8b","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:40.9589706+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_oydsee6qg8b","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:41.7841744+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_7z6tmg66hsr","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:41.7852113+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_7z6tmg66hsr","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:45.3714572+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_ndxjowqsy1p","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:45.3730811+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_ndxjowqsy1p","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:46.0707437+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_etqd0w7i5f","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:46.0718228+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_etqd0w7i5f","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:46.663739+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_6ginzg49czc","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:46.6648149+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_6ginzg49czc","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:47.3047117+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_ywka5ktysq","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:47.3068036+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_ywka5ktysq","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:47.9885456+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_kl6ia8rl10n","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:47.9895595+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_kl6ia8rl10n","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:48.7713957+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_kli8jmtb3w","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:48.7719007+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_kli8jmtb3w","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:49.3856421+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_3ooqg7fnk92","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:22:49.3866868+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_3ooqg7fnk92","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:03.9424987+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_sha30gbxyce","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:03.9440651+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_sha30gbxyce","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:04.8975093+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_b7ktrmkwq7","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:04.8991097+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_b7ktrmkwq7","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:05.5929294+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_8cgbqgixmir","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:05.5956603+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_8cgbqgixmir","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:06.4496761+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_nvj0e8rtixn","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:06.4512442+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_nvj0e8rtixn","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:07.144322+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_bsuw413ufsj","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:07.1469271+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_bsuw413ufsj","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:07.9979501+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_6r241cu0yha","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:08.0005742+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_6r241cu0yha","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:08.6428144+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_vds56yk5uy","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:08.6433318+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_vds56yk5uy","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:09.2601606+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_pos0h7fsjz9","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:09.2617254+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_pos0h7fsjz9","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:09.9835085+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_s4kyocpxn","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:09.9840154+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_s4kyocpxn","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:10.564777+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_o9lzivdh6dt","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:10.5668609+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_o9lzivdh6dt","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:11.2036157+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_8zdlee7yyil","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:11.2057435+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_8zdlee7yyil","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:12.881582+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":240},"msg":"Request cancellation initiated","session_id":"40629c91-8ee6-4551-aada-fdba83298f44"}
{"time":"2025-08-02T16:23:32.2182901+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:23:33.2938623+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_7hdcm7zxzub","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:33.29597+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_7hdcm7zxzub","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:33.2985795+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:23:44.3380744+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_km4r7kz5ex","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:44.3412552+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_km4r7kz5ex","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:46.1132632+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:23:49.4431369+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_dq9ntmv7nu","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:49.4447374+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_dq9ntmv7nu","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:51.0141495+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:23:53.8843644+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_2p6pi1il2m","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:53.8853955+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_2p6pi1il2m","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:23:59.6740897+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"mkdir -p \"D:\\AIGC-dm\\桌面窗口翻译程序\\src\\config\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\src\\core\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\src\\gui\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\src\\ai_engines\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\src\\utils\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\resources\\icons\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\resources\\styles\" \"D:\\AIGC-dm\\桌面窗口翻译程序\\tests\"","err":null}
{"time":"2025-08-02T16:23:59.6761837+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:24:06.6794333+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_f83carn8to9","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:24:06.6809669+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_f83carn8to9","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:24:08.107544+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:24:23.1530366+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_jxyoto5d5qe","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:24:23.1535651+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_jxyoto5d5qe","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:24:59.1637058+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:25:46.5883697+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_1sorhsf6v9z","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:25:46.5894216+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_1sorhsf6v9z","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:27.9113534+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:26:31.2422207+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_2ykibxhwxo4","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:31.2432838+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_2ykibxhwxo4","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:33.1709976+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:26:39.9236229+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_b2ibenh09og","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:39.9257149+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_b2ibenh09og","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:41.30318+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:26:54.3005431+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":626},"msg":"Tool call started","toolCall":{"id":"call_xl99vjful9b","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:26:54.314527+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":633},"msg":"Finished tool call","toolCall":{"id":"call_xl99vjful9b","name":"","input":"","type":"","finished":false}}
{"time":"2025-08-02T16:27:53.7652895+08:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/provider.(*anthropicClient).convertMessages","file":"/home/<USER>/work/crush/crush/internal/llm/provider/anthropic.go","line":156},"msg":"There is a message without content, investigate, this should not happen"}
{"time":"2025-08-02T16:27:55.5297439+08:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/cmd.init.func2","file":"/home/<USER>/work/crush/crush/internal/cmd/root.go","line":75},"msg":"TUI run error","error":"program was killed: program experienced a panic"}
{"time":"2025-08-02T16:27:55.5297439+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).Cancel","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":240},"msg":"Request cancellation initiated","session_id":"40629c91-8ee6-4551-aada-fdba83298f44"}
