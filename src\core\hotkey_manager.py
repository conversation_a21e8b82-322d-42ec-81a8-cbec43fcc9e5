"""
Hotkey manager for handling global keyboard shortcuts.
"""

import keyboard
from PyQt6.QtCore import QObject, pyqtSignal
import threading
from ..utils.logger import get_logger

logger = get_logger(__name__)

class HotkeyManager(QObject):
    """Manages global hotkeys in a separate thread."""
    
    hotkey_triggered = pyqtSignal(str)  # Signal emitted when a hotkey is triggered
    
    def __init__(self):
        super().__init__()
        self._hotkeys = {}
        self._running = False
        self._thread = None
        
    def register_hotkey(self, name: str, key_combination: str) -> None:
        """Register a hotkey with a given name."""
        if name in self._hotkeys:
            self.unregister_hotkey(name)
        
        try:
            callback = lambda: self.hotkey_triggered.emit(name)
            keyboard.add_hotkey(key_combination, callback)
            self._hotkeys[name] = key_combination
            logger.info(f"Hotkey '{name}' registered for '{key_combination}'")
        except Exception as e:
            logger.error(f"Failed to register hotkey '{key_combination}': {e}")
            
    def unregister_hotkey(self, name: str) -> None:
        """Unregister a hotkey by name."""
        if name in self._hotkeys:
            key_combination = self._hotkeys.pop(name)
            try:
                keyboard.remove_hotkey(key_combination)
                logger.info(f"Hotkey '{name}' for '{key_combination}' unregistered")
            except Exception as e:
                logger.error(f"Failed to unregister hotkey '{key_combination}': {e}")

    def start(self):
        """Start listening for hotkeys in a background thread."""
        if not self._running:
            self._running = True
            self._thread = threading.Thread(target=self._listen, daemon=True)
            self._thread.start()
            logger.info("Hotkey manager started")
            
    def stop(self):
        """Stop listening for hotkeys."""
        self._running = False
        # No direct way to stop keyboard.wait(), but daemon thread will exit
        # when the main application exits.
        logger.info("Hotkey manager stopped")
        
    def _listen(self):
        """Internal listen loop."""
        # This is a placeholder as keyboard library handles its own thread.
        # The start method is sufficient to begin listening.
        pass
