"""
Test suite for the cache manager module.
"""

import pytest
import time
import tempfile
import shutil
from pathlib import Path
from src.utils.cache_manager import CacheManager
from src.ai_engines.base_engine import TranslationResult

class TestCacheManager:
    """Test cases for CacheManager."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.cache_manager = CacheManager(self.temp_dir, expiry_time=3600)  # 1 hour
    
    def teardown_method(self):
        """Cleanup test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """Test cache manager initialization."""
        assert self.cache_manager.cache_dir.exists()
        assert self.cache_manager.expiry_time == 3600
        assert isinstance(self.cache_manager._memory_cache, dict)
    
    def test_generate_key(self):
        """Test cache key generation."""
        key1 = self.cache_manager._generate_key("Hello", "openai", "zh")
        key2 = self.cache_manager._generate_key("Hello", "openai", "zh")
        key3 = self.cache_manager._generate_key("Hello", "claude", "zh")
        
        assert key1 == key2
        assert key1 != key3
        assert len(key1) == 32  # MD5 hash length
    
    def test_is_cache_valid(self):
        """Test cache validity check."""
        current_time = time.time()
        
        # Valid cache
        assert self.cache_manager._is_cache_valid(current_time - 1800)  # 30 minutes ago
        
        # Expired cache
        assert not self.cache_manager._is_cache_valid(current_time - 7200)  # 2 hours ago
    
    def test_get_set_memory_cache(self):
        """Test getting and setting from memory cache."""
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        
        # Set cache
        self.cache_manager.set(result)
        
        # Get from cache
        cached_result = self.cache_manager.get("Hello", "openai", "es")
        
        assert cached_result is not None
        assert cached_result.original_text == result.original_text
        assert cached_result.translated_text == result.translated_text
        assert cached_result.engine == result.engine
    
    def test_get_cache_miss(self):
        """Test cache miss."""
        result = self.cache_manager.get("Nonexistent", "openai", "zh")
        assert result is None
    
    def test_get_expired_cache(self):
        """Test getting expired cache entry."""
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        
        # Set with old timestamp
        key = self.cache_manager._generate_key("Hello", "openai", "es")
        self.cache_manager._memory_cache[key] = {
            'timestamp': time.time() - 7200,  # 2 hours ago
            'result': {
                'original_text': result.original_text,
                'translated_text': result.translated_text,
                'source_language': result.source_language,
                'target_language': result.target_language,
                'engine': result.engine,
                'confidence': result.confidence,
                'metadata': result.metadata or {}
            }
        }
        
        # Try to get - should return None due to expiry
        cached_result = self.cache_manager.get("Hello", "openai", "es")
        assert cached_result is None
        
        # Should be removed from memory cache
        assert key not in self.cache_manager._memory_cache
    
    def test_file_cache_operations(self):
        """Test file cache operations."""
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        
        # Clear memory cache to test file cache
        self.cache_manager._memory_cache.clear()
        
        # Set cache (writes to file)
        self.cache_manager.set(result)
        
        # Clear memory cache again
        self.cache_manager._memory_cache.clear()
        
        # Get from file cache
        cached_result = self.cache_manager.get("Hello", "openai", "es")
        
        assert cached_result is not None
        assert cached_result.original_text == result.original_text
        assert cached_result.translated_text == result.translated_text
        
        # Should now be in memory cache
        assert len(self.cache_manager._memory_cache) == 1
    
    def test_clear_cache(self):
        """Test clearing all cache."""
        # Add some entries
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        self.cache_manager.set(result)
        
        # Verify entries exist
        assert len(self.cache_manager._memory_cache) > 0
        assert len(list(self.cache_manager.cache_dir.glob("*.json"))) > 0
        
        # Clear cache
        self.cache_manager.clear()
        
        # Verify cache is cleared
        assert len(self.cache_manager._memory_cache) == 0
        assert len(list(self.cache_manager.cache_dir.glob("*.json"))) == 0
    
    def test_cleanup_expired(self):
        """Test cleanup of expired cache entries."""
        # Add expired entry
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        
        key = self.cache_manager._generate_key("Hello", "openai", "es")
        self.cache_manager._memory_cache[key] = {
            'timestamp': time.time() - 7200,  # 2 hours ago
            'result': {
                'original_text': result.original_text,
                'translated_text': result.translated_text,
                'source_language': result.source_language,
                'target_language': result.target_language,
                'engine': result.engine,
                'confidence': result.confidence,
                'metadata': result.metadata or {}
            }
        }
        
        # Add valid entry
        result2 = TranslationResult(
            original_text="World",
            translated_text="Mundo",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        self.cache_manager.set(result2)
        
        # Verify both entries exist
        assert len(self.cache_manager._memory_cache) == 2
        
        # Cleanup expired
        self.cache_manager.cleanup_expired()
        
        # Verify only valid entry remains
        assert len(self.cache_manager._memory_cache) == 1
        cached_result = self.cache_manager.get("World", "openai", "es")
        assert cached_result is not None
    
    def test_get_stats(self):
        """Test getting cache statistics."""
        # Add some entries
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9
        )
        self.cache_manager.set(result)
        
        stats = self.cache_manager.get_stats()
        
        assert 'memory_entries' in stats
        assert 'file_entries' in stats
        assert 'total_entries' in stats
        assert 'cache_dir' in stats
        assert 'expiry_time' in stats
        assert stats['memory_entries'] >= 0
        assert stats['file_entries'] >= 0
        assert stats['total_entries'] == stats['memory_entries'] + stats['file_entries']
    
    def test_cache_with_none_metadata(self):
        """Test caching result with None metadata."""
        result = TranslationResult(
            original_text="Hello",
            translated_text="Hola",
            source_language="en",
            target_language="es",
            engine="openai",
            confidence=0.9,
            metadata=None
        )
        
        self.cache_manager.set(result)
        cached_result = self.cache_manager.get("Hello", "openai", "es")
        
        assert cached_result is not None
        assert cached_result.metadata is not None
        assert cached_result.metadata == {}
    
    def test_cache_corrupted_file(self):
        """Test handling of corrupted cache files."""
        # Create a corrupted cache file
        key = self.cache_manager._generate_key("Hello", "openai", "es")
        cache_file = self.cache_manager.cache_dir / f"{key}.json"
        
        with open(cache_file, 'w') as f:
            f.write("corrupted json content")
        
        # Try to get - should handle gracefully
        result = self.cache_manager.get("Hello", "openai", "es")
        assert result is None
        
        # File should be removed
        assert not cache_file.exists()