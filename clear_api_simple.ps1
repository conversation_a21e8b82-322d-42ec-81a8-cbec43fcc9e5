# 简化版API密钥清理脚本

Write-Host "API Key Cleaner" -ForegroundColor Green
Write-Host "==============="

# API变量列表
$apiVars = @(
    "CLAUDE_API_KEY",
    "ANTHROPIC_API_KEY", 
    "OPENAI_API_KEY",
    "BAIDU_API_KEY",
    "BAIDU_SECRET_KEY",
    "QWEN_API_KEY"
)

Write-Host "Checking for API keys..."

$found = @()
foreach ($var in $apiVars) {
    $userVal = [Environment]::GetEnvironmentVariable($var, "User")
    $sysVal = [Environment]::GetEnvironmentVariable($var, "Machine")
    
    if ($userVal) {
        Write-Host "Found User: $var" -ForegroundColor Yellow
        $found += @{Name=$var; Scope="User"}
    }
    if ($sysVal) {
        Write-Host "Found System: $var" -ForegroundColor Red
        $found += @{Name=$var; Scope="Machine"}
    }
}

if ($found.Count -eq 0) {
    Write-Host "No API keys found!" -ForegroundColor Green
    exit 0
}

Write-Host "Found $($found.Count) API keys"
$confirm = Read-Host "Clear all? (y/N)"

if ($confirm -eq "y") {
    Write-Host "Clearing API keys..."
    
    foreach ($item in $found) {
        try {
            [Environment]::SetEnvironmentVariable($item.Name, $null, $item.Scope)
            Write-Host "Cleared: $($item.Name) ($($item.Scope))" -ForegroundColor Green
        }
        catch {
            Write-Host "Failed: $($item.Name) ($($item.Scope))" -ForegroundColor Red
        }
    }
    
    Write-Host "Done! Please restart PowerShell." -ForegroundColor Green
} else {
    Write-Host "Cancelled." -ForegroundColor Yellow
}
