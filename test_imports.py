#!/usr/bin/env python3
"""
测试导入脚本 - 验证所有依赖是否正确安装
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_imports():
    """测试所有关键导入"""
    try:
        print("🔍 测试PyQt6导入...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer
        from PyQt6.QtGui import QIcon
        print("✅ PyQt6导入成功")
        
        print("🔍 测试项目模块导入...")
        from src.config import Settings, AIEngine
        print("✅ 配置模块导入成功")
        
        from src.core import ClipboardMonitor, TextProcessor, Translator
        print("✅ 核心模块导入成功")
        
        from src.gui import FloatingWindow, SettingsDialog
        print("✅ GUI模块导入成功")
        
        from src.utils.logger import get_logger
        print("✅ 工具模块导入成功")
        
        print("\n🎉 所有导入测试通过！项目可以正常运行。")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
