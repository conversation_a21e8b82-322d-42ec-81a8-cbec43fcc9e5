"""
Application constants and enums.
"""

from enum import Enum
from typing import Dict, List

class AIEngine(Enum):
    """Supported AI translation engines."""
    OPENAI = "openai"
    CLAUDE = "claude"
    BAIDU = "baidu"
    QWEN = "qwen"

class Language(Enum):
    """Supported languages for translation."""
    AUTO = "auto"
    ZH = "zh"  # Chinese
    EN = "en"  # English
    JA = "ja"  # Japanese
    KO = "ko"  # Korean
    FR = "fr"  # French
    DE = "de"  # German
    ES = "es"  # Spanish
    RU = "ru"  # Russian

class EventType(Enum):
    """Application event types."""
    CLIPBOARD_CHANGED = "clipboard_changed"
    TRANSLATION_COMPLETE = "translation_complete"
    ERROR_OCCURRED = "error_occurred"
    SHOW_TRANSLATION = "show_translation"
    HIDE_TRANSLATION = "hide_translation"

# Window position constants
POSITION_OFFSET_X = 20
POSITION_OFFSET_Y = 20
MIN_WINDOW_WIDTH = 200
MIN_WINDOW_HEIGHT = 100

# Text processing constants
MAX_TEXT_LENGTH = 5000
MIN_TEXT_LENGTH = 1
DEBOUNCE_DELAY = 50  # milliseconds

# Cache configuration
CACHE_EXPIRY = 86400  # 24 hours in seconds
MAX_CACHE_SIZE = 1000

# AI API timeouts
API_TIMEOUT = 30  # seconds
MAX_RETRIES = 3

# Supported file types for translation
TEXT_FILE_TYPES = {'.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml'}

# Default prompts for different AI engines
DEFAULT_PROMPTS = {
    AIEngine.OPENAI: "请将以下文本翻译成中文，仅返回翻译结果，无需额外说明或格式：\n\n{text}",
    AIEngine.CLAUDE: "请将以下文本翻译成中文，要求准确自然，保持专业术语一致性：\n\n{text}",
    AIEngine.BAIDU: "请翻译以下文本为中文：\n\n{text}",
    AIEngine.QWEN: "请将以下文本翻译成中文，保持语义准确：\n\n{text}"
}

# Language detection patterns
LANGUAGE_PATTERNS = {
    'zh': r'[\u4e00-\u9fff]',  # Chinese characters
    'ja': r'[\u3040-\u309f\u30a0-\u30ff]',  # Japanese characters
    'ko': r'[\uac00-\ud7af]',  # Korean characters
    'en': r'[a-zA-Z]',  # English letters
    'ru': r'[\u0400-\u04ff]',  # Cyrillic characters
}

# Error messages
ERROR_MESSAGES = {
    'api_error': 'AI服务暂时不可用，请稍后重试',
    'network_error': '网络连接错误，请检查网络设置',
    'timeout_error': '翻译请求超时，请稍后重试',
    'empty_text': '请输入要翻译的文本',
    'text_too_long': f'文本过长，最多支持{MAX_TEXT_LENGTH}个字符',
    'engine_not_configured': 'AI引擎未配置，请在设置中配置API密钥',
}