{"ai": {"default_engine": "custom", "openai_api_key": "", "openai_model": "gpt-3.5-turbo", "openai_base_url": "https://api.openai.com/v1", "claude_api_key": "", "claude_model": "claude-3-sonnet-20240229", "baidu_api_key": "", "baidu_secret_key": "", "qwen_api_key": "", "qwen_model": "qwen-turbo", "custom_name": "Cheery Studio", "custom_api_key": "sk-eAvXeLuCrteG9VYFFraw4KPIsuglwI1u9BIFo3vJKVe5SCdW", "custom_base_url": "https://kmqepuqihpft.ap-southeast-1.clawcloudrun.com/v1", "custom_model": "gemini-2.5-flash", "available_custom_models": ["gemini-2.5-flash", "gemini-2.5-flash-lite-preview-06-17", "GLM-4.5", "horizon-beta"]}, "window": {"width": 400, "height": 300, "max_width": 600, "max_height": 400, "margin": 20, "opacity": 1.0, "font_size": 14, "font_family": "Microsoft YaHei", "background_color": "#2b2b2b", "text_color": "#ffffff", "border_color": "#555555", "border_radius": 8}, "behavior": {"auto_hide_delay": 0, "debounce_delay": 500, "max_text_length": 5000, "min_text_length": 1, "enable_sensitive_filter": false, "enable_cache": true, "cache_expiry": 86400, "max_retries": 3, "timeout": 30, "mouse_follow_interval": 3, "mouse_follow_threshold": 2, "enable_mouse_follow": true, "smooth_follow": false, "use_high_performance": true, "display_sync": true, "vsync_mode": true, "hotkey_toggle_window": "H"}, "enabled_engines": ["openai"]}