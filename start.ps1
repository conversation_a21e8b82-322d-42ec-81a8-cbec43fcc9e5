# 桌面翻译程序 PowerShell启动脚本

Write-Host "正在启动桌面翻译程序..." -ForegroundColor Green

# 检查Python是否可用
$pythonFound = $false
$pythonCommand = ""

# 尝试不同的Python命令
foreach ($cmd in @("python", "python3", "py")) {
    try {
        $version = & $cmd --version 2>$null
        if ($version) {
            Write-Host "找到Python: $cmd" -ForegroundColor Yellow
            $pythonCommand = $cmd
            $pythonFound = $true
            break
        }
    } catch {
        continue
    }
}

if ($pythonFound) {
    Write-Host "使用 $pythonCommand 启动程序..." -ForegroundColor Green
    try {
        & $pythonCommand simple_main.py
    } catch {
        Write-Host "程序启动失败: $_" -ForegroundColor Red
        Read-Host "按任意键退出"
    }
} else {
    Write-Host "错误：未找到Python解释器" -ForegroundColor Red
    Write-Host "请确保Python已安装并添加到PATH环境变量中" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "或者尝试以下命令之一：" -ForegroundColor Yellow
    Write-Host "  python simple_main.py"
    Write-Host "  python3 simple_main.py"
    Write-Host "  py simple_main.py"
    Read-Host "按任意键退出"
}