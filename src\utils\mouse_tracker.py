"""
Mouse tracking utility for window positioning.
"""

import sys
from typing import <PERSON><PERSON>, Optional
from PyQt6.QtCore import QPoint, QTimer
from PyQt6.QtGui import QCursor

class MouseTracker:
    """Tracks mouse position for window positioning."""
    
    def __init__(self):
        self.current_pos = QPoint()
        self.screen_geometry = None
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_position)
        self.update_timer.start(50)  # Update every 50ms
    
    def _update_position(self) -> None:
        """Update current mouse position."""
        self.current_pos = QCursor.pos()
    
    def get_position(self) -> QPoint:
        """Get current mouse position."""
        return self.current_pos
    
    def get_screen_geometry(self) -> Optional[Tuple[int, int, int, int]]:
        """Get screen geometry (x, y, width, height)."""
        if not self.screen_geometry:
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                screen = app.primaryScreen()
                geometry = screen.geometry()
                self.screen_geometry = (
                    geometry.x(), geometry.y(),
                    geometry.width(), geometry.height()
                )
        
        return self.screen_geometry
    
    def calculate_window_position(
        self, 
        window_width: int, 
        window_height: int,
        offset_x: int = 20,
        offset_y: int = 20
    ) -> Tuple[int, int]:
        """Calculate optimal window position near cursor."""
        cursor_x = self.current_pos.x()
        cursor_y = self.current_pos.y()
        
        screen_geom = self.get_screen_geometry()
        if not screen_geom:
            return cursor_x + offset_x, cursor_y + offset_y
        
        screen_x, screen_y, screen_width, screen_height = screen_geom
        
        # Try to position window to the right and below cursor
        x = cursor_x + offset_x
        y = cursor_y + offset_y
        
        # Adjust if window would go off screen
        if x + window_width > screen_x + screen_width:
            x = cursor_x - window_width - offset_x
        
        if y + window_height > screen_y + screen_height:
            y = cursor_y - window_height - offset_y
        
        # Ensure window stays within screen bounds
        x = max(screen_x, min(x, screen_x + screen_width - window_width))
        y = max(screen_y, min(y, screen_y + screen_height - window_height))
        
        return x, y
    
    def is_cursor_near_edge(self, threshold: int = 50) -> bool:
        """Check if cursor is near screen edge."""
        cursor_x = self.current_pos.x()
        cursor_y = self.current_pos.y()
        
        screen_geom = self.get_screen_geometry()
        if not screen_geom:
            return False
        
        screen_x, screen_y, screen_width, screen_height = screen_geom
        
        near_left = cursor_x - screen_x < threshold
        near_right = screen_x + screen_width - cursor_x < threshold
        near_top = cursor_y - screen_y < threshold
        near_bottom = screen_y + screen_height - cursor_y < threshold
        
        return near_left or near_right or near_top or near_bottom
    
    def stop(self) -> None:
        """Stop tracking mouse position."""
        self.update_timer.stop()