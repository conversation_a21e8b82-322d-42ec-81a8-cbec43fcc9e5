"""
Cache management for translation results.
"""

import json
import time
import hashlib
from typing import Dict, Optional, Any
from pathlib import Path
from dataclasses import asdict

from ..ai_engines.base_engine import TranslationResult

class CacheManager:
    """Manages translation cache."""
    
    def __init__(self, cache_dir: str = "cache", expiry_time: int = 86400):
        self.cache_dir = Path(cache_dir)
        self.expiry_time = expiry_time
        self.cache_dir.mkdir(exist_ok=True)
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
    
    def _generate_key(self, text: str, engine: str, target_lang: str) -> str:
        """Generate cache key for translation request."""
        content = f"{text}:{engine}:{target_lang}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _is_cache_valid(self, timestamp: float) -> bool:
        """Check if cache entry is still valid."""
        return time.time() - timestamp < self.expiry_time
    
    def get(self, text: str, engine: str, target_lang: str) -> Optional[TranslationResult]:
        """Get cached translation result."""
        key = self._generate_key(text, engine, target_lang)
        
        # Check memory cache first
        if key in self._memory_cache:
            cache_data = self._memory_cache[key]
            if self._is_cache_valid(cache_data['timestamp']):
                return TranslationResult(**cache_data['result'])
            else:
                del self._memory_cache[key]
        
        # Check file cache
        cache_file = self.cache_dir / f"{key}.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                if self._is_cache_valid(cache_data['timestamp']):
                    # Store in memory cache
                    self._memory_cache[key] = cache_data
                    return TranslationResult(**cache_data['result'])
                else:
                    cache_file.unlink()
            
            except (json.JSONDecodeError, KeyError, TypeError):
                if cache_file.exists():
                    cache_file.unlink()
        
        return None
    
    def set(self, result: TranslationResult) -> None:
        """Cache translation result."""
        key = self._generate_key(
            result.original_text, 
            result.engine, 
            result.target_language
        )
        
        cache_data = {
            'timestamp': time.time(),
            'result': asdict(result)
        }
        
        # Store in memory cache
        self._memory_cache[key] = cache_data
        
        # Store in file cache
        cache_file = self.cache_dir / f"{key}.json"
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def clear(self) -> None:
        """Clear all cache."""
        self._memory_cache.clear()
        
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                cache_file.unlink()
            except Exception:
                pass
    
    def cleanup_expired(self) -> None:
        """Remove expired cache entries."""
        current_time = time.time()
        
        # Clean memory cache
        expired_keys = [
            key for key, data in self._memory_cache.items()
            if not self._is_cache_valid(data['timestamp'])
        ]
        for key in expired_keys:
            del self._memory_cache[key]
        
        # Clean file cache
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                if not self._is_cache_valid(cache_data['timestamp']):
                    cache_file.unlink()
            
            except (json.JSONDecodeError, KeyError, TypeError):
                if cache_file.exists():
                    cache_file.unlink()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        file_count = len(list(self.cache_dir.glob("*.json")))
        memory_count = len(self._memory_cache)
        
        return {
            'memory_entries': memory_count,
            'file_entries': file_count,
            'total_entries': memory_count + file_count,
            'cache_dir': str(self.cache_dir),
            'expiry_time': self.expiry_time
        }