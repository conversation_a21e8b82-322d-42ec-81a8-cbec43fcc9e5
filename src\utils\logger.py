"""
Logging utility for the application.
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional

class Logger:
    """Application logger configuration."""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self._configured = False
    
    def configure(
        self,
        level: str = "INFO",
        rotation: str = "10 MB",
        retention: str = "7 days",
        compression: str = "zip"
    ) -> None:
        """Configure logger with file and console handlers."""
        if self._configured:
            return
        
        # Remove default handler
        logger.remove()
        
        # Console handler
        logger.add(
            sys.stdout,
            level=level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # General log file
        logger.add(
            self.log_dir / "app.log",
            level=level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=rotation,
            retention=retention,
            compression=compression,
            encoding="utf-8"
        )
        
        # Error log file
        logger.add(
            self.log_dir / "errors.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation=rotation,
            retention=retention,
            compression=compression,
            encoding="utf-8"
        )
        
        self._configured = True
    
    def get_logger(self, name: Optional[str] = None):
        """Get logger instance."""
        if not self._configured:
            self.configure()
        
        if name:
            return logger.bind(name=name)
        return logger

# Global logger instance
app_logger = Logger()

def get_logger(name: Optional[str] = None):
    """Get application logger."""
    return app_logger.get_logger(name)