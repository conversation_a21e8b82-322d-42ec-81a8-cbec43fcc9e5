# PowerShell脚本：清除系统中的API密钥环境变量
# 使用方法：在PowerShell中运行 .\clear_api_keys.ps1

Write-Host "🧹 API密钥清理工具" -ForegroundColor Green
Write-Host "=" * 50

# 定义要检查和清除的API环境变量
$apiVars = @(
    "CLAUDE_API_KEY",
    "ANTHROPIC_API_KEY", 
    "OPENAI_API_KEY",
    "BAIDU_API_KEY",
    "BAIDU_SECRET_KEY",
    "QWEN_API_KEY",
    "GEMINI_API_KEY",
    "COHERE_API_KEY",
    "HUGGINGFACE_API_KEY",
    "TOGETHER_API_KEY",
    "REPLICATE_API_TOKEN",
    "PALM_API_KEY",
    "AI21_API_KEY"
)

# 函数：安全地删除环境变量
function Remove-EnvironmentVariable {
    param(
        [string]$Name,
        [string]$Scope
    )
    
    try {
        [Environment]::SetEnvironmentVariable($Name, $null, $Scope)
        Write-Host "✅ 已清除 $Scope 级变量: $Name" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ 清除失败 $Scope 级变量: $Name - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 第一步：检查现有的API环境变量
Write-Host "`n🔍 检查当前API相关环境变量..." -ForegroundColor Cyan

$foundVars = @()
$userVars = @()
$systemVars = @()

foreach ($var in $apiVars) {
    $userValue = [Environment]::GetEnvironmentVariable($var, "User")
    $systemValue = [Environment]::GetEnvironmentVariable($var, "Machine")
    
    if ($userValue) {
        $maskedValue = if ($userValue.Length -gt 10) { 
            $userValue.Substring(0, 10) + "..." 
        } else { 
            $userValue 
        }
        Write-Host "👤 用户级: $var = $maskedValue" -ForegroundColor Yellow
        $foundVars += @{Name = $var; Scope = "User"; Value = $userValue}
        $userVars += $var
    }
    
    if ($systemValue) {
        $maskedValue = if ($systemValue.Length -gt 10) { 
            $systemValue.Substring(0, 10) + "..." 
        } else { 
            $systemValue 
        }
        Write-Host "🖥️  系统级: $var = $maskedValue" -ForegroundColor Red
        $foundVars += @{Name = $var; Scope = "Machine"; Value = $systemValue}
        $systemVars += $var
    }
}

if ($foundVars.Count -eq 0) {
    Write-Host "✅ 未发现任何API相关环境变量" -ForegroundColor Green
    Write-Host "🎉 系统已经是干净的状态！"
    exit 0
}

# 显示发现的变量总结
Write-Host "`n📊 发现的API环境变量总结:" -ForegroundColor Cyan
Write-Host "  👤 用户级变量: $($userVars.Count) 个"
Write-Host "  🖥️  系统级变量: $($systemVars.Count) 个"
Write-Host "  📋 总计: $($foundVars.Count) 个"

# 第二步：询问用户确认
Write-Host "`n⚠️  警告:" -ForegroundColor Yellow
Write-Host "  即将清除所有发现的API密钥环境变量"
Write-Host "  这将影响所有使用这些API密钥的应用程序"
Write-Host ""

$confirmation = Read-Host "是否继续清除? (y/N)"

if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "❌ 操作已取消" -ForegroundColor Yellow
    exit 0
}

# 第三步：清除环境变量
Write-Host "`n🧹 开始清除API环境变量..." -ForegroundColor Cyan

$clearedCount = 0
$failedCount = 0

foreach ($varInfo in $foundVars) {
    $name = $varInfo.Name
    $scope = $varInfo.Scope
    
    Write-Host "🗑️  清除 $scope 级变量: $name" -ForegroundColor Gray
    
    if (Remove-EnvironmentVariable -Name $name -Scope $scope) {
        $clearedCount++
    } else {
        $failedCount++
    }
}

# 第四步：验证清除结果
Write-Host "`n🔍 验证清除结果..." -ForegroundColor Cyan

$remainingVars = @()
foreach ($var in $apiVars) {
    $userValue = [Environment]::GetEnvironmentVariable($var, "User")
    $systemValue = [Environment]::GetEnvironmentVariable($var, "Machine")
    
    if ($userValue -or $systemValue) {
        $remainingVars += $var
        if ($userValue) {
            Write-Host "⚠️  仍存在用户级: $var" -ForegroundColor Yellow
        }
        if ($systemValue) {
            Write-Host "⚠️  仍存在系统级: $var" -ForegroundColor Yellow
        }
    }
}

# 第五步：显示最终结果
Write-Host "`n📊 清除结果总结:" -ForegroundColor Green
Write-Host "  ✅ 成功清除: $clearedCount 个"
Write-Host "  ❌ 清除失败: $failedCount 个"
Write-Host "  ⚠️  仍然存在: $($remainingVars.Count) 个"

if ($remainingVars.Count -eq 0) {
    Write-Host "`n🎉 所有API密钥环境变量已成功清除！" -ForegroundColor Green
    Write-Host "💡 建议重新启动PowerShell以确保更改生效"
} else {
    Write-Host "`n⚠️  部分变量清除失败，可能需要管理员权限" -ForegroundColor Yellow
    Write-Host "💡 对于系统级变量，请以管理员身份运行此脚本"
}

Write-Host "`n🔒 安全提醒:" -ForegroundColor Cyan
Write-Host "  1. API密钥已从环境变量中清除"
Write-Host "  2. 如果有应用程序依赖这些密钥，可能需要重新配置"
Write-Host "  3. 建议检查 .env 文件和应用程序配置文件"

Write-Host "`n✨ API密钥清理完成！" -ForegroundColor Green
