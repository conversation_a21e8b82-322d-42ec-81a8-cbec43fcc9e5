"""
Floating translation window widget.
"""

import asyncio
from typing import Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QScrollArea, QFrame, QApplication
)
from PyQt6.QtCore import Qt, QTimer, QPoint, pyqtSignal
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QCursor

from ..config.settings import Settings
from ..utils.mouse_tracker import MouseTracker
from ..utils.logger import get_logger

logger = get_logger(__name__)

class FloatingWindow(QWidget):
    """Floating translation window that follows mouse cursor."""
    
    translation_requested = pyqtSignal(str)  # Signal for translation requests
    
    def __init__(self, settings: Settings):
        super().__init__()
        self.settings = settings
        self.window_settings = settings.window
        self.behavior_settings = settings.behavior
        self.mouse_tracker = MouseTracker()
        self.current_text = ""
        self.translated_text = ""
        self.is_visible = False
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_window)
        
        # 高性能位置跟踪定时器 - 优化的鼠标跟随
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self._update_position_smooth)
        
        # 高性能鼠标跟随设置
        if self.behavior_settings.enable_mouse_follow:
            # 使用高精度定时器（完全兼容性处理）
            try:
                # 尝试PyQt6新版本
                if hasattr(Qt, 'TimerType'):
                    self.position_timer.setTimerType(Qt.TimerType.PreciseTimer)
                elif hasattr(self.position_timer, 'setTimerType'):
                    # 尝试其他版本
                    self.position_timer.setTimerType(1)  # 1 = PreciseTimer
            except (AttributeError, TypeError):
                # 完全兼容性处理，跳过高精度设置
                logger.debug("Timer precision setting not available, using default")
            
            self.position_timer.start(self.behavior_settings.mouse_follow_interval)
        
        self.last_mouse_pos = None  # 记录上次鼠标位置
        self.move_threshold = self.behavior_settings.mouse_follow_threshold
        self.target_pos = None  # 目标位置（用于平滑移动）
        self.smooth_factor = 0.8 if self.behavior_settings.smooth_follow else 1.0  # 平滑因子
        
        # 高性能优化标志
        self.high_performance = getattr(self.behavior_settings, 'use_high_performance', True)
        self.frame_skip_counter = 0  # 帧跳过计数器
        
        self._setup_ui()
        self._setup_window_properties()
        
        logger.info("Floating window initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(5)
        
        # Original text label
        self.original_label = QLabel()
        self.original_label.setWordWrap(True)
        self.original_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.original_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 12px;
                border-bottom: 1px solid #444444;
                padding-bottom: 5px;
                margin-bottom: 5px;
            }
        """)
        
        # Translation text scroll area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        self.translation_label = QLabel()
        self.translation_label.setWordWrap(True)
        self.translation_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.translation_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                background: transparent;
            }
        """)
        
        self.scroll_area.setWidget(self.translation_label)
        
        # Add to layout
        self.layout.addWidget(self.original_label)
        self.layout.addWidget(self.scroll_area)
        
        # Initially hide
        self.hide()
    
    def _setup_window_properties(self) -> None:
        """Setup window properties."""
        self.setWindowFlags(
            Qt.WindowType.Tool | 
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Set size
        self.setMaximumSize(
            self.window_settings.max_width, 
            self.window_settings.max_height
        )
        
        # Set font
        font = QFont(self.window_settings.font_family, self.window_settings.font_size)
        self.translation_label.setFont(font)
        self.original_label.setFont(
            QFont(self.window_settings.font_family, self.window_settings.font_size - 2)
        )
    
    def show_translation(self, original_text: str, translated_text: str) -> None:
        """Show translation in the floating window."""
        try:
            logger.info(f"显示翻译窗口: {original_text[:30]}... -> {translated_text[:30]}...")
            
            # 强制隐藏窗口先
            self.hide()
            QApplication.processEvents()  # 确保隐藏完成
            
            # 强制清除之前的内容
            self.current_text = ""
            self.translated_text = ""
            
            # 更新内容
            self.current_text = original_text
            self.translated_text = translated_text
            
            # 动态生成唯一标识（时间戳）
            import time
            timestamp = str(int(time.time() * 1000))[-4:]
            
            # 强制清空和更新文本 - 添加时间戳确保内容变化
            self.original_label.clear()
            self.translation_label.clear()
            
            # 设置新内容（包含时间戳确保唯一性）
            original_display = f"原文[{timestamp}]: {original_text[:80]}..."
            self.original_label.setText(original_display)
            self.translation_label.setText(f"[{timestamp}] {translated_text}")
            
            # 改变窗口标题确保可见更新
            self.setWindowTitle(f"翻译窗口 - {timestamp}")
            
            # 多次强制刷新
            self.original_label.update()
            self.translation_label.update()
            self.original_label.repaint()
            self.translation_label.repaint()
            
            # Calculate optimal size
            self._adjust_window_size()
            
            # 强制移动到鼠标位置（确保可见）
            cursor = QApplication.primaryScreen().availableGeometry()
            mouse_pos = self.cursor().pos()
            self.move(mouse_pos.x() + 20, mouse_pos.y() + 20)
            
            # 强制显示窗口并置顶
            self.show()
            self.raise_()
            self.activateWindow()
            self.setWindowFlags(
                Qt.WindowType.FramelessWindowHint | 
                Qt.WindowType.WindowStaysOnTopHint |
                Qt.WindowType.Tool
            )
            self.show()  # 重新显示应用新的flags
            self.is_visible = True
            
            # 强制重绘和事件处理
            self.update()
            self.repaint()
            QApplication.processEvents()
            
            # 额外确保窗口在最前面
            self.raise_()
            self.activateWindow()
            
            # Start hide timer only if auto_hide_delay > 0
            if self.behavior_settings.auto_hide_delay > 0:
                self.hide_timer.start(self.behavior_settings.auto_hide_delay)
            
            logger.info(f"✅ 翻译窗口强制刷新完成[{timestamp}] - 原文: {original_text[:30]}")
            logger.info(f"🎯 窗口位置: ({self.x()}, {self.y()}), 大小: {self.width()}x{self.height()}")
            
        except Exception as e:
            logger.error(f"显示翻译窗口失败: {e}")
            import traceback
            traceback.print_exc()
    
    def hide_window(self) -> None:
        """Hide the floating window."""
        self.hide()
        self.is_visible = False
        self.hide_timer.stop()
        # 重置跟随状态
        self.last_mouse_pos = None
        self.target_pos = None
        logger.debug("Translation window hidden")
    
    def _adjust_window_size(self) -> None:
        """Adjust window size based on content."""
        # Get text metrics
        font_metrics = self.translation_label.fontMetrics()
        
        # Calculate ideal size
        text_width = font_metrics.horizontalAdvance(self.translated_text)
        text_height = font_metrics.height() * (
            self.translated_text.count('\n') + 1
        )
        
        # Add padding
        ideal_width = min(
            text_width + 40, 
            self.window_settings.max_width
        )
        ideal_height = min(
            text_height + 60, 
            self.window_settings.max_height
        )
        
        # Ensure minimum size
        ideal_width = max(ideal_width, 200)
        ideal_height = max(ideal_height, 100)
        
        # Set size
        self.resize(ideal_width, ideal_height)
    
    def _position_window(self) -> None:
        """Position window near mouse cursor."""
        x, y = self.mouse_tracker.calculate_window_position(
            self.width(), 
            self.height(),
            self.window_settings.margin,
            self.window_settings.margin
        )
        
        self.move(x, y)
    
    def _update_position_smooth(self) -> None:
        """超高性能鼠标跟随 - 60FPS优化版本"""
        if not (self.is_visible and self.isVisible()):
            return
        
        # 高性能模式：每3帧跳过2帧，保持20FPS实际更新
        if self.high_performance:
            self.frame_skip_counter += 1
            if self.frame_skip_counter % 3 != 0:
                return
        
        # 超快速鼠标位置获取（内联优化）
        current_mouse_pos = QCursor.pos()
        
        # 快速移动检测（最小计算）
        if self.last_mouse_pos is not None:
            dx = current_mouse_pos.x() - self.last_mouse_pos.x()
            dy = current_mouse_pos.y() - self.last_mouse_pos.y()
            
            # 超快距离检测（避免abs调用）
            if -self.move_threshold < dx < self.move_threshold and -self.move_threshold < dy < self.move_threshold:
                return
        
        # 超快位置计算（内联优化，避免函数调用）
        cursor_x = current_mouse_pos.x()
        cursor_y = current_mouse_pos.y()
        
        # 简化的位置计算
        offset_x = self.window_settings.margin
        offset_y = self.window_settings.margin
        
        target_x = cursor_x + offset_x
        target_y = cursor_y + offset_y
        
        # 简单边界检测（快速版本）
        screen = QApplication.primaryScreen().geometry()
        if target_x + self.width() > screen.width():
            target_x = cursor_x - self.width() - offset_x
        if target_y + self.height() > screen.height():
            target_y = cursor_y - self.height() - offset_y
            
        # 确保在屏幕内
        target_x = max(0, min(target_x, screen.width() - self.width()))
        target_y = max(0, min(target_y, screen.height() - self.height()))
        
        # 直接移动（最快方式）
        self.move(target_x, target_y)
        
        # 更新记录
        self.last_mouse_pos = current_mouse_pos
    
    def paintEvent(self, event) -> None:
        """Paint the window background and border."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw background with high visibility
        background_color = QColor(30, 30, 30, 240)  # 深灰色，高不透明度
        painter.setBrush(QBrush(background_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(
            0, 0, self.width(), self.height(),
            self.window_settings.border_radius,
            self.window_settings.border_radius
        )
        
        # Draw bright border for visibility
        border_color = QColor(0, 200, 255, 255)  # 亮蓝色边框
        painter.setPen(QPen(border_color, 2))  # 更粗的边框
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawRoundedRect(
            1, 1, self.width() - 2, self.height() - 2,
            self.window_settings.border_radius,
            self.window_settings.border_radius
        )
    
    def enterEvent(self, event) -> None:
        """Handle mouse enter event."""
        # Stop hide timer when mouse enters window
        if self.hide_timer.isActive():
            self.hide_timer.stop()
        super().enterEvent(event)
    
    def leaveEvent(self, event) -> None:
        """Handle mouse leave event."""
        # Restart hide timer when mouse leaves window (only if auto_hide enabled)
        if self.is_visible and self.behavior_settings.auto_hide_delay > 0:
            self.hide_timer.start(self.behavior_settings.auto_hide_delay)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event) -> None:
        """Handle mouse press event."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Allow dragging the window
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event) -> None:
        """Handle mouse move event."""
        if hasattr(self, 'dragging') and self.dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event) -> None:
        """Handle mouse release event."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()
    
    def update_settings(self, settings: Settings) -> None:
        """Update window settings."""
        self.settings = settings
        self.window_settings = settings.window
        self.behavior_settings = settings.behavior
        self._setup_window_properties()
        self.setStyleSheet(self._get_stylesheet())
    
    def _get_stylesheet(self) -> str:
        """Get window stylesheet."""
        return f"""
            QWidget {{
                background-color: rgba(43, 43, 43, {self.window_settings.opacity});
                border: 1px solid {self.window_settings.border_color};
                border-radius: {self.window_settings.border_radius}px;
            }}
        """
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        self.mouse_tracker.stop()
        super().closeEvent(event)