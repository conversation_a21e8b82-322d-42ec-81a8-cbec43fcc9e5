"""
Floating translation window widget.
"""

import asyncio
from typing import Op<PERSON>
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QScrollArea, QFrame, QApplication
)
from PyQt6.QtCore import Qt, QTimer, QPoint, pyqtSignal, QRect
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QCursor, QTextOption

from ..config.settings import Settings
from ..utils.mouse_tracker import MouseTracker
from ..utils.logger import get_logger

logger = get_logger(__name__)

class FloatingWindow(QWidget):
    """Floating translation window that follows mouse cursor."""
    
    translation_requested = pyqtSignal(str)  # Signal for translation requests
    
    def __init__(self, settings: Settings):
        super().__init__()
        self.settings = settings
        self.window_settings = settings.window
        self.behavior_settings = settings.behavior
        self.mouse_tracker = MouseTracker()
        self.current_text = ""
        self.translated_text = ""
        self.is_visible = False
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_window)
        
        # 高性能位置跟踪定时器 - 优化的鼠标跟随
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self._update_position_smooth)
        
        # 高性能鼠标跟随设置
        if self.behavior_settings.enable_mouse_follow:
            # 使用高精度定时器（完全兼容性处理）
            try:
                # 尝试PyQt6新版本
                if hasattr(Qt, 'TimerType'):
                    self.position_timer.setTimerType(Qt.TimerType.PreciseTimer)
                elif hasattr(self.position_timer, 'setTimerType'):
                    # 尝试其他版本
                    self.position_timer.setTimerType(1)  # 1 = PreciseTimer
            except (AttributeError, TypeError):
                # 完全兼容性处理，跳过高精度设置
                logger.debug("Timer precision setting not available, using default")
            
            self.position_timer.start(self.behavior_settings.mouse_follow_interval)
        
        self.last_mouse_pos = None  # 记录上次鼠标位置
        self.move_threshold = self.behavior_settings.mouse_follow_threshold
        self.target_pos = None  # 目标位置（用于平滑移动）
        self.smooth_factor = 0.8 if self.behavior_settings.smooth_follow else 1.0  # 平滑因子
        
        # 高性能优化标志
        self.high_performance = getattr(self.behavior_settings, 'use_high_performance', True)
        self.frame_skip_counter = 0  # 帧跳过计数器
        
        self._setup_ui()
        self._setup_window_properties()
        
        logger.info("Floating window initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(6, 6, 6, 6)  # 减少边距使窗口更紧凑
        self.layout.setSpacing(3)  # 减少标签间距
        
        # Original text label
        self.original_label = QLabel()
        self.original_label.setWordWrap(True)
        self.original_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.original_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 12px;
                border: none; /* Ensure no border */
                padding-bottom: 5px;
                margin-bottom: 5px;
                background: transparent;
            }
        """)
        
        # Translation text scroll area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff) # Disable vertical scrollbar
        self.scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
                margin: 0px;
                padding: 0px;
            }
        """)
        
        self.translation_label = QLabel()
        self.translation_label.setWordWrap(True)
        self.translation_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.translation_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                background: transparent;
                border: none; /* Ensure no border */
                margin: 0px;
                padding: 0px;
            }
        """)
        
        self.scroll_area.setWidget(self.translation_label)
        
        # Add to layout
        self.layout.addWidget(self.original_label)
        self.layout.addWidget(self.scroll_area)
        
        # Initially hide
        self.hide()
    
    def _setup_window_properties(self) -> None:
        """Setup window properties."""
        self.setWindowFlags(
            Qt.WindowType.Tool | 
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground) # Re-enable this
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Set initial window opacity
        self.setWindowOpacity(self.window_settings.opacity)
        
        # Set font
        font = QFont(self.window_settings.font_family, self.window_settings.font_size)
        self.translation_label.setFont(font)
        self.translation_label.setWordWrap(True)  # 启用自动换行
        self.original_label.setFont(
            QFont(self.window_settings.font_family, self.window_settings.font_size - 2)
        )
        self.original_label.setWordWrap(True)  # 启用自动换行
    
    def show_translation(self, original_text: str, translated_text: str) -> None:
        """Show translation in the floating window."""
        try:
            logger.info(f"🚀 进入 show_translation 函数")
            logger.info(f"📝 原文: '{original_text}' (长度: {len(original_text)})")
            logger.info(f"📝 译文: '{translated_text}' (长度: {len(translated_text)})")
            logger.info(f"🎯 窗口当前状态: visible={self.isVisible()}, hidden={self.isHidden()}")
            
            # 更新内容
            self.current_text = original_text
            self.translated_text = translated_text
            
            logger.info("🔄 开始调整窗口大小...")
            # 调整窗口大小（同时设置文本内容）
            self._adjust_window_size()
            logger.info(f"📐 窗口大小调整完成: {self.width()}x{self.height()}")
            
            # 获取鼠标位置并定位窗口
            mouse_pos = self.cursor().pos()
            logger.info(f"🖱️ 鼠标位置: ({mouse_pos.x()}, {mouse_pos.y()})")
            
            # 简单定位到鼠标附近
            self.move(mouse_pos.x() + 20, mouse_pos.y() + 20)
            logger.info(f"📍 窗口移动到: ({self.x()}, {self.y()})")
            
            # 显示窗口
            logger.info("👁️ 开始显示窗口...")
            self.show()
            self.raise_()
            self.is_visible = True
            
            logger.info(f"✅ 窗口显示完成 - 位置: ({self.x()}, {self.y()}), 大小: {self.width()}x{self.height()}, 可见: {self.isVisible()}")
            
            # Start hide timer only if auto_hide_delay > 0
            if self.behavior_settings.auto_hide_delay > 0:
                self.hide_timer.start(self.behavior_settings.auto_hide_delay)
            
        except Exception as e:
            logger.error(f"❌ 显示翻译窗口失败: {e}")
            import traceback
            traceback.print_exc()
    
    def hide_window(self) -> None:
        """Hide the floating window."""
        self.hide()
        self.is_visible = False
        self.hide_timer.stop()
        # 重置跟随状态
        self.last_mouse_pos = None
        self.target_pos = None
        logger.debug("Translation window hidden")
    
    def _adjust_window_size(self) -> None:
        """超精确自适应窗口大小 - 严格按照文本内容计算"""
        if not self.current_text or not self.translated_text:
            return
            
        # 设置文本内容
        original_display = f"原文: {self.current_text}"
        self.original_label.setText(original_display)
        self.translation_label.setText(self.translated_text)
        
        # 获取字体度量
        original_metrics = self.original_label.fontMetrics()
        translation_metrics = self.translation_label.fontMetrics()
        
        # 计算单行文本的实际宽度（不换行的情况）
        original_width = original_metrics.boundingRect(original_display).width()
        translation_width = translation_metrics.boundingRect(self.translated_text).width()
        
        # 取最大宽度作为理想宽度
        ideal_width = max(original_width, translation_width)
        
        # 添加实际布局边距（左右各6px = 12px）
        content_width = ideal_width + 12
        
        # 针对短文本进行特殊优化
        char_count = len(self.current_text) + len(self.translated_text)
        if char_count <= 6:  # 很短的文本（1-3个字）
            min_width = 60
        elif char_count <= 12:  # 中等长度文本（4-6个字）
            min_width = 70
        else:  # 长文本
            min_width = 80
            
        # 设置最小宽度和最大宽度限制
        content_width = max(min_width, min(content_width, self.window_settings.max_width))
        
        # 计算实际文本显示区域宽度
        text_area_width = content_width - 12
        
        # 计算文本高度（考虑换行）
        original_height = self._calculate_wrapped_text_height(original_display, self.original_label.font(), text_area_width)
        translation_height = self._calculate_wrapped_text_height(self.translated_text, self.translation_label.font(), text_area_width)
        
        # 计算总高度（实际布局边距：上下各6px = 12px + 中间间距3px）
        content_height = original_height + translation_height + 3 + 12  # 布局间距3px + 上下边距12px
        content_height = max(30, content_height)  # 最小高度30px
        
        # 设置窗口尺寸
        self.resize(content_width, content_height)
        
        logger.debug(f"超精确窗口尺寸: {content_width}x{content_height} | 原文:{original_width}px 译文:{translation_width}px")
    
    def _calculate_wrapped_text_height(self, text: str, font, width: int) -> int:
        """计算文本在指定宽度下的换行高度"""
        from PyQt6.QtGui import QFontMetrics, QTextOption
        from PyQt6.QtCore import QRectF
        
        font_metrics = QFontMetrics(font)
        
        # 如果文本很短，直接返回单行高度
        single_line_width = font_metrics.boundingRect(text).width()
        if single_line_width <= width:
            return font_metrics.height()
        
        # 对于长文本，使用精确的换行计算
        rect = font_metrics.boundingRect(0, 0, width, 0, Qt.TextFlag.TextWordWrap, text)
        return rect.height()
    
    def _position_window(self) -> None:
        """Position window near mouse cursor."""
        x, y = self.mouse_tracker.calculate_window_position(
            self.width(), 
            self.height(),
            self.window_settings.margin,
            self.window_settings.margin
        )
        
        self.move(x, y)
    
    def _update_position_smooth(self) -> None:
        """超高性能鼠标跟随 - 60FPS优化版本"""
        if not (self.is_visible and self.isVisible()):
            return
        
        # 高性能模式：每3帧跳过2帧，保持20FPS实际更新
        if self.high_performance:
            self.frame_skip_counter += 1
            if self.frame_skip_counter % 3 != 0:
                return
        
        # 超快速鼠标位置获取（内联优化）
        current_mouse_pos = QCursor.pos()
        
        # 快速移动检测（最小计算）
        if self.last_mouse_pos is not None:
            dx = current_mouse_pos.x() - self.last_mouse_pos.x()
            dy = current_mouse_pos.y() - self.last_mouse_pos.y()
            
            # 超快距离检测（避免abs调用）
            if -self.move_threshold < dx < self.move_threshold and -self.move_threshold < dy < self.move_threshold:
                return
        
        # 超快位置计算（内联优化，避免函数调用）
        cursor_x = current_mouse_pos.x()
        cursor_y = current_mouse_pos.y()
        
        # 简化的位置计算
        offset_x = self.window_settings.margin
        offset_y = self.window_settings.margin
        
        target_x = cursor_x + offset_x
        target_y = cursor_y + offset_y
        
        # 简单边界检测（快速版本）
        screen = QApplication.primaryScreen().geometry()
        if target_x + self.width() > screen.width():
            target_x = cursor_x - self.width() - offset_x
        if target_y + self.height() > screen.height():
            target_y = cursor_y - self.height() - offset_y
            
        # 确保在屏幕内
        target_x = max(0, min(target_x, screen.width() - self.width()))
        target_y = max(0, min(target_y, screen.height() - self.height()))
        
        # 直接移动（最快方式）
        self.move(target_x, target_y)
        
        # 更新记录
        self.last_mouse_pos = current_mouse_pos
    
    def paintEvent(self, event) -> None:
        """Paint the window background and border."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Use the configured background color and opacity
        bg_color = QColor(self.window_settings.background_color)
        # bg_color.setAlpha(int(self.window_settings.opacity * 255)) # Removed - opacity controlled by setWindowOpacity
        painter.setBrush(QBrush(bg_color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(
            0, 0, self.width(), self.height(),
            self.window_settings.border_radius,
            self.window_settings.border_radius
        )
        
        # Draw bright border for visibility (disabled for no border requirement)
        # border_color = QColor(self.window_settings.border_color) # Use configured border color
        # border_color.setAlpha(int(self.window_settings.opacity * 255)) # Removed - opacity controlled by setWindowOpacity
        # painter.setPen(QPen(border_color, 2))  # 更粗的边框
        # painter.setBrush(Qt.BrushStyle.NoBrush)
        # painter.drawRoundedRect(
        #     1, 1, self.width() - 2, self.height() - 2,
        #     self.window_settings.border_radius,
        #     self.window_settings.border_radius
        # )
    
    def enterEvent(self, event) -> None:
        """Handle mouse enter event."""
        # Stop hide timer when mouse enters window
        if self.hide_timer.isActive():
            self.hide_timer.stop()
        super().enterEvent(event)
    
    def leaveEvent(self, event) -> None:
        """Handle mouse leave event."""
        # Restart hide timer when mouse leaves window (only if auto_hide enabled)
        if self.is_visible and self.behavior_settings.auto_hide_delay > 0:
            self.hide_timer.start(self.behavior_settings.auto_hide_delay)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event) -> None:
        """Handle mouse press event."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Allow dragging the window
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event) -> None:
        """Handle mouse move event."""
        if hasattr(self, 'dragging') and self.dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event) -> None:
        """Handle mouse release event."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()
    
    def update_settings(self, settings: Settings) -> None:
        """Update window settings."""
        self.settings = settings
        self.window_settings = settings.window
        self.behavior_settings = settings.behavior
        self.setWindowOpacity(self.window_settings.opacity) # Apply opacity when settings are updated
        self._setup_window_properties()
        self.setStyleSheet(self._get_stylesheet())
    
    def _get_stylesheet(self) -> str:
        """Get window stylesheet."""
        return f"""
            QWidget {{
                background-color: rgba(43, 43, 43, {self.window_settings.opacity});
                border: none; /* Remove window border */
                border-radius: {self.window_settings.border_radius}px;
            }}
        """
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        self.mouse_tracker.stop()
        super().closeEvent(event)