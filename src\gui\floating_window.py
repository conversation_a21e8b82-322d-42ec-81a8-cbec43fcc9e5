"""
Floating translation window widget.
"""

import asyncio
from typing import Op<PERSON>
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, QTimer, QPoint, pyqtSignal
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QCursor

from ..config import WindowSettings
from ..utils.mouse_tracker import MouseTracker
from ..utils.logger import get_logger

logger = get_logger(__name__)

class FloatingWindow(QWidget):
    """Floating translation window that follows mouse cursor."""
    
    translation_requested = pyqtSignal(str)  # Signal for translation requests
    
    def __init__(self, settings: WindowSettings):
        super().__init__()
        self.settings = settings
        self.mouse_tracker = MouseTracker()
        self.current_text = ""
        self.translated_text = ""
        self.is_visible = False
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_window)
        
        self._setup_ui()
        self._setup_window_properties()
        
        logger.info("Floating window initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(5)
        
        # Original text label
        self.original_label = QLabel()
        self.original_label.setWordWrap(True)
        self.original_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.original_label.setStyleSheet("""
            QLabel {
                color: #888888;
                font-size: 12px;
                border-bottom: 1px solid #444444;
                padding-bottom: 5px;
                margin-bottom: 5px;
            }
        """)
        
        # Translation text scroll area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        self.translation_label = QLabel()
        self.translation_label.setWordWrap(True)
        self.translation_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        self.translation_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                background: transparent;
            }
        """)
        
        self.scroll_area.setWidget(self.translation_label)
        
        # Add to layout
        self.layout.addWidget(self.original_label)
        self.layout.addWidget(self.scroll_area)
        
        # Initially hide
        self.hide()
    
    def _setup_window_properties(self) -> None:
        """Setup window properties."""
        self.setWindowFlags(
            Qt.WindowType.Tool | 
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        
        # Set size
        self.setMaximumSize(
            self.settings.max_width, 
            self.settings.max_height
        )
        
        # Set font
        font = QFont(self.settings.font_family, self.settings.font_size)
        self.translation_label.setFont(font)
        self.original_label.setFont(
            QFont(self.settings.font_family, self.settings.font_size - 2)
        )
    
    def show_translation(self, original_text: str, translated_text: str) -> None:
        """Show translation in the floating window."""
        self.current_text = original_text
        self.translated_text = translated_text
        
        # Update text
        self.original_label.setText(f"原文: {original_text[:100]}...")
        self.translation_label.setText(translated_text)
        
        # Calculate optimal size
        self._adjust_window_size()
        
        # Position window near cursor
        self._position_window()
        
        # Show window
        self.show()
        self.raise_()
        self.is_visible = True
        
        # Start hide timer
        self.hide_timer.start(self.settings.auto_hide_delay)
        
        logger.debug("Translation window shown")
    
    def hide_window(self) -> None:
        """Hide the floating window."""
        self.hide()
        self.is_visible = False
        self.hide_timer.stop()
        logger.debug("Translation window hidden")
    
    def _adjust_window_size(self) -> None:
        """Adjust window size based on content."""
        # Get text metrics
        font_metrics = self.translation_label.fontMetrics()
        
        # Calculate ideal size
        text_width = font_metrics.horizontalAdvance(self.translated_text)
        text_height = font_metrics.height() * (
            self.translated_text.count('\n') + 1
        )
        
        # Add padding
        ideal_width = min(
            text_width + 40, 
            self.settings.max_width
        )
        ideal_height = min(
            text_height + 60, 
            self.settings.max_height
        )
        
        # Ensure minimum size
        ideal_width = max(ideal_width, 200)
        ideal_height = max(ideal_height, 100)
        
        # Set size
        self.resize(ideal_width, ideal_height)
    
    def _position_window(self) -> None:
        """Position window near mouse cursor."""
        x, y = self.mouse_tracker.calculate_window_position(
            self.width(), 
            self.height(),
            self.settings.margin,
            self.settings.margin
        )
        
        self.move(x, y)
    
    def paintEvent(self, event) -> None:
        """Paint the window background and border."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw background
        painter.setBrush(QBrush(QColor(self.settings.background_color)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(
            0, 0, self.width(), self.height(),
            self.settings.border_radius,
            self.settings.border_radius
        )
        
        # Draw border
        painter.setPen(QPen(QColor(self.settings.border_color), 1))
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawRoundedRect(
            0, 0, self.width() - 1, self.height() - 1,
            self.settings.border_radius,
            self.settings.border_radius
        )
    
    def enterEvent(self, event) -> None:
        """Handle mouse enter event."""
        # Stop hide timer when mouse enters window
        if self.hide_timer.isActive():
            self.hide_timer.stop()
        super().enterEvent(event)
    
    def leaveEvent(self, event) -> None:
        """Handle mouse leave event."""
        # Restart hide timer when mouse leaves window
        if self.is_visible:
            self.hide_timer.start(self.settings.auto_hide_delay)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event) -> None:
        """Handle mouse press event."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Allow dragging the window
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event) -> None:
        """Handle mouse move event."""
        if hasattr(self, 'dragging') and self.dragging:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event) -> None:
        """Handle mouse release event."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            event.accept()
    
    def update_settings(self, settings: WindowSettings) -> None:
        """Update window settings."""
        self.settings = settings
        self._setup_window_properties()
        self.setStyleSheet(self._get_stylesheet())
    
    def _get_stylesheet(self) -> str:
        """Get window stylesheet."""
        return f"""
            QWidget {{
                background-color: rgba(43, 43, 43, {self.settings.opacity});
                border: 1px solid {self.settings.border_color};
                border-radius: {self.settings.border_radius}px;
            }}
        """
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        self.mouse_tracker.stop()
        super().closeEvent(event)