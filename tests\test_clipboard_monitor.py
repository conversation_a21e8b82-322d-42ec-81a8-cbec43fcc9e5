"""
Test suite for the clipboard monitor module.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from PyQt6.QtCore import QObject, pyqtSignal

from src.core.clipboard_monitor import ClipboardMonitor

class TestClipboardMonitor:
    """Test cases for ClipboardMonitor."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.monitor = ClipboardMonitor()
    
    def teardown_method(self):
        """Cleanup test fixtures."""
        asyncio.run(self.monitor.stop())
    
    def test_initialization(self):
        """Test monitor initialization."""
        assert not self.monitor._running
        assert self.monitor._last_text == ""
        assert len(self.monitor._callbacks) == 0
    
    def test_add_callback(self):
        """Test adding callback functions."""
        callback = Mock()
        self.monitor.add_callback(callback)
        
        assert callback in self.monitor._callbacks
        assert len(self.monitor._callbacks) == 1
    
    def test_remove_callback(self):
        """Test removing callback functions."""
        callback = Mock()
        self.monitor.add_callback(callback)
        self.monitor.remove_callback(callback)
        
        assert callback not in self.monitor._callbacks
        assert len(self.monitor._callbacks) == 0
    
    @pytest.mark.asyncio
    async def test_start_stop(self):
        """Test starting and stopping monitor."""
        assert not self.monitor._running
        
        await self.monitor.start()
        assert self.monitor._running
        assert self.monitor._monitor_task is not None
        
        await self.monitor.stop()
        assert not self.monitor._running
        assert self.monitor._monitor_task is None
    
    def test_should_process_change_valid_text(self):
        """Test text change validation with valid text."""
        text = "Hello, world!"
        self.monitor._last_change_time = 0
        
        result = self.monitor._should_process_change(text)
        assert result is True
    
    def test_should_process_change_empty_text(self):
        """Test text change validation with empty text."""
        text = ""
        
        result = self.monitor._should_process_change(text)
        assert result is False
    
    def test_should_process_change_short_text(self):
        """Test text change validation with short text."""
        text = "H"
        
        result = self.monitor._should_process_change(text)
        assert result is False
    
    def test_should_process_change_sensitive_text(self):
        """Test text change validation with sensitive text."""
        text = "my password is secret123"
        
        result = self.monitor._should_process_change(text)
        assert result is False
    
    def test_should_process_change_debounce(self):
        """Test text change validation with debouncing."""
        import time
        text = "Hello, world!"
        self.monitor._last_change_time = time.time()
        self.monitor._debounce_delay = 1000  # 1 second
        
        result = self.monitor._should_process_change(text)
        assert result is False
    
    def test_is_sensitive_text_password(self):
        """Test sensitive text detection for passwords."""
        assert self.monitor._is_sensitive_text("my password is secret")
        assert self.monitor._is_sensitive_text("enter passwd:123")
        assert self.monitor._is_sensitive_text("pwd: test")
    
    def test_is_sensitive_text_credit_card(self):
        """Test sensitive text detection for credit cards."""
        assert self.monitor._is_sensitive_text("1234 5678 9012 3456")
        assert self.monitor._is_sensitive_text("1234-5678-9012-3456")
    
    def test_is_sensitive_text_api_keys(self):
        """Test sensitive text detection for API keys."""
        assert self.monitor._is_sensitive_text("sk-123456789")
        assert self.monitor._is_sensitive_text("AIzaSyC-123456")
    
    def test_is_sensitive_text_normal_text(self):
        """Test sensitive text detection for normal text."""
        assert not self.monitor._is_sensitive_text("Hello, world!")
        assert not self.monitor._is_sensitive_text("This is normal text")
    
    def test_set_debounce_delay(self):
        """Test setting debounce delay."""
        self.monitor.set_debounce_delay(1000)
        assert self.monitor._debounce_delay == 1000
    
    def test_get_last_text(self):
        """Test getting last detected text."""
        text = "Test text"
        self.monitor._last_text = text
        
        assert self.monitor.get_last_text() == text